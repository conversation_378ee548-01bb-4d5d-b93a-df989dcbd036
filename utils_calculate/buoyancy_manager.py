import numpy as np
import torch
import trimesh
from pxr import UsdGeom

import isaacsim.core.utils.prims as prim_utils
import omni.kit.commands
import omni
from isaacsim.core.utils.rotations import gf_quat_to_np_array
from pxr import UsdGeom, Usd, Gf
from omni.isaac.dynamic_control import _dynamic_control

class BuoyancyManager:
    def __init__(self, rigid_prim_view, robot_for_com, world, root_body_path, geometry_prim_path):
        print("浮力管理器初始化...")
        self.view = rigid_prim_view
        self.robot_com_provider = robot_for_com
        self.world = world
        self.root_body_path = root_body_path
        self.geometry_prim_path = geometry_prim_path
        
        self.FLUID_DENSITY = 1000.0
        self.GRAVITY_MAG = 9.81
        
        self.local_vertices = None
        self.local_faces = None
        
        self.root_body_idx_in_view = -1
        
        self._is_initialized = False

    def initialize(self):
        print("正在执行 BuoyancyManager 的一次性初始化...")
        if self.world.get_physics_context():
            self.world.get_physics_context().set_gravity(0.0)
            print("✅ 已禁用Isaac Sim自动重力。")
        
        try:
            # RigidPrimView.prim_paths 是一个 list of strings
            self.root_body_idx_in_view = self.view.prim_paths.index(self.root_body_path)
            print(f"✅ 在视图中找到根刚体 '{self.root_body_path}'，索引为: {self.root_body_idx_in_view}")
        except ValueError:
            raise RuntimeError(f"无法在RigidPrimView的路径列表中找到根刚体 '{self.root_body_path}'。视图路径: {self.view.prim_paths}")
            
        self.cache_mesh_data_from_cube()
        
        self._is_initialized = True
        print("✅ BuoyancyManager 初始化完成。")

    def cache_mesh_data_from_cube(self):
        # 此函数已经过验证，是正确的
        print(f"正在从 {self.geometry_prim_path} 的Cube属性缓存网格数据...")
        prim = prim_utils.get_prim_at_path(self.geometry_prim_path)
        if not prim.IsValid(): raise ValueError(f"在路径 {self.geometry_prim_path} 处找不到有效的Prim。")
        print(f"  - Prim类型为: {prim.GetTypeName()}")
        size_attr = prim.GetAttribute("size")
        if not size_attr.IsValid() or size_attr.Get() is None:
             extent_attr = prim.GetAttribute("extent")
             if not extent_attr.IsValid() or extent_attr.Get() is None:
                raise ValueError(f"Cube {self.geometry_prim_path} 既没有 'size' 也没有 'extent' 属性。")
             extent = np.array(extent_attr.Get())
             size_vec = extent[1] - extent[0]
        else:
            size = size_attr.Get()
            size_vec = np.array([size, size, size])
        half_size = size_vec / 2.0
        print(f"  - Cube 半尺寸: {half_size}")
        p = half_size
        vertices = np.array([
            [-p[0], -p[1], -p[2]], [ p[0], -p[1], -p[2]], [ p[0],  p[1], -p[2]], [-p[0],  p[1], -p[2]],
            [-p[0], -p[1],  p[2]], [ p[0], -p[1],  p[2]], [ p[0],  p[1],  p[2]], [-p[0],  p[1],  p[2]],
        ])
        xformable = UsdGeom.Xformable(prim)
        time_code = Usd.TimeCode.Default()
        transform_matrix_gf = xformable.GetLocalTransformation(time_code)
        if transform_matrix_gf != Gf.Matrix4d(1):
            print("  - 应用本地变换...")
            transform_matrix = np.array(transform_matrix_gf)
            vertices_hom = np.hstack([vertices, np.ones((vertices.shape[0], 1))])
            transformed_vertices_hom = vertices_hom @ transform_matrix
            vertices = transformed_vertices_hom[:, :3]
        self.local_vertices = vertices.astype(np.float32)
        self.local_faces = np.array([
            [0, 3, 2], [2, 1, 0], [4, 5, 6], [6, 7, 4], [0, 1, 5], [5, 4, 0],
            [2, 3, 7], [7, 6, 2], [1, 2, 6], [6, 5, 1], [3, 0, 4], [4, 7, 3],
        ], dtype=np.int32)
        print(f"  - 通过数学计算缓存了 {len(self.local_vertices)} 个顶点和 {len(self.local_faces)} 个面。")

    def update(self):
        if not self._is_initialized:
            self.initialize()
            return
        if self.local_vertices is None or self.local_faces is None: return
            
        num_bodies = self.view.count
        total_forces = torch.zeros((num_bodies, 3), device=self.world.device, dtype=torch.float32)
        total_torques = torch.zeros((num_bodies, 3), device=self.world.device, dtype=torch.float32)
            
        # --- 关键修复：假设get_masses返回numpy，并立即转换 ---
        masses_np = self.view.get_masses()
        if masses_np is None: return
        masses = torch.as_tensor(masses_np, dtype=torch.float32, device=self.world.device)
        
        total_forces[:, 2] = -masses * self.GRAVITY_MAG

        # --- 关键修复：假设get_world_poses返回numpy，并立即转换 ---
        all_positions_np, all_orientations_np = self.view.get_world_poses()
        if all_positions_np is None: return
        
        current_position_np = all_positions_np[self.root_body_idx_in_view]
        current_orientation_np = all_orientations_np[self.root_body_idx_in_view]
        
        current_z_height = current_position_np[2]
        if self.world.current_time_step_index % 60 == 0:
             print(f"当前Z轴高度: {current_z_height:.4f} m")

        current_position = torch.as_tensor(current_position_np, dtype=torch.float32, device=self.world.device)
        current_orientation = torch.as_tensor(current_orientation_np, dtype=torch.float32, device=self.world.device)
        vertices_tensor = torch.from_numpy(self.local_vertices).to(device=self.world.device, dtype=torch.float32)

        rotated_vertices = self.quat_rotate(current_orientation, vertices_tensor)
        world_vertices = rotated_vertices + current_position
        world_vertices_np = world_vertices.cpu().numpy()

        mesh = trimesh.Trimesh(vertices=world_vertices_np, faces=self.local_faces)
        water_plane_normal = np.array([0.0, 0.0, 1.0])
        water_plane_origin = np.array([0.0, 0.0, 0.0])
        try:
            submerged_mesh = mesh.slice_plane(plane_origin=water_plane_origin, plane_normal=water_plane_normal)
        except Exception:
            submerged_mesh = None
        
        if submerged_mesh is not None and not submerged_mesh.is_empty and submerged_mesh.volume > 1e-6:
            buoyancy_force_mag = submerged_mesh.volume * self.FLUID_DENSITY * self.GRAVITY_MAG
            if self.world.current_time_step_index % 60 == 0:
                print(
                    f"  💧 [已淹没] Z: {current_z_height:.4f} m | "
                    f"体积: {submerged_mesh.volume:.6f} m³ | "
                    f"浮力: {buoyancy_force_mag:.2f} N"
                )
            buoyancy_force_vec = torch.tensor([0.0, 0.0, buoyancy_force_mag], device=self.world.device, dtype=torch.float32)
            
            # 假设get_com返回numpy
            all_coms_np, _ = self.robot_com_provider.get_com()
            if all_coms_np is None: return
            
            com_position_np = all_coms_np[self.root_body_idx_in_view]
            com_position = torch.as_tensor(com_position_np, dtype=torch.float32, device=self.world.device)
            center_of_buoyancy = torch.as_tensor(submerged_mesh.centroid, dtype=torch.float32, device=self.world.device)
            
            r = center_of_buoyancy - com_position
            torque_vec = torch.cross(r, buoyancy_force_vec)
            
            total_forces[self.root_body_idx_in_view, :] += buoyancy_force_vec
            total_torques[self.root_body_idx_in_view, :] += torque_vec

        self.view.apply_forces_and_torques(forces=total_forces, torques=total_torques)

    def quat_rotate(self, q_tensor, v_tensor):
        q_w = q_tensor[0]
        q_xyz = q_tensor[1:]
        q_xyz_expanded = q_xyz.unsqueeze(0)
        t = 2.0 * torch.cross(q_xyz_expanded, v_tensor, dim=-1)
        v_prime = v_tensor + q_w * t + torch.cross(q_xyz_expanded, t, dim=-1)
        return v_prime