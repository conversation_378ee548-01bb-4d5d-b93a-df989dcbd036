from pxr import Usd, UsdGeom, Gf
import numpy as np

def get_prim_scale(prim):
    """获取 prim 的 scale（默认 [1,1,1]）"""
    scale = [1.0, 1.0, 1.0]
    xform_api = UsdGeom.Xformable(prim)
    for op in xform_api.GetOrderedXformOps():
        if op.GetOpType() == UsdGeom.XformOp.TypeScale:
            scale = op.Get()
    return scale

def compute_mesh_volume(prim, scale):
    """用三角形面积积分法计算 Mesh 体积（带 scale 修正）"""
    mesh = UsdGeom.Mesh(prim)
    points = np.array(mesh.GetPointsAttr().Get(), dtype=np.float64)
    faces = mesh.GetFaceVertexIndicesAttr().Get()
    counts = mesh.GetFaceVertexCountsAttr().Get()

    # 应用 scale 到顶点坐标
    points *= np.array(scale)

    volume = 0.0
    idx = 0
    for c in counts:
        if c == 3:  # 三角形
            v0, v1, v2 = points[faces[idx]], points[faces[idx+1]], points[faces[idx+2]]
            volume += np.dot(np.cross(v0, v1), v2) / 6.0
        else:
            # 如果是多边形，先三角化
            for i in range(1, c - 1):
                v0, v1, v2 = points[faces[idx]], points[faces[idx+i]], points[faces[idx+i+1]]
                volume += np.dot(np.cross(v0, v1), v2) / 6.0
        idx += c
    return abs(volume)

def compute_prim_volume(prim):
    """计算任意几何体 prim 的体积（自动读取 scale）"""
    scale = get_prim_scale(prim)

    if prim.IsA(UsdGeom.Mesh):
        return compute_mesh_volume(prim, scale)

    return 0.0

def compute_prim_volume_simple(prim):
    """计算任意简化prim 的体积（读取 scale）"""
    mesh = UsdGeom.Mesh(prim)
    scale = get_prim_scale(prim)
    
    bboxCache = UsdGeom.BBoxCache(Usd.TimeCode.Default(), [UsdGeom.Tokens.default_])
    bbox = bboxCache.ComputeWorldBound(mesh.GetPrim())
    min_corner = bbox.box.min  
    max_corner = bbox.box.max  
    extent = max_corner - min_corner
    print("长宽高:", extent[0], extent[1], extent[2])
    if extent is not None:
        return extent[0] * extent[1] *extent[2]
    else:
        print(f"[警告] Mesh {prim.GetPath()} 没有 volume 属性，返回 0")
        return 0.0
    return 0.0

def get_robot_volume(stage, prim_paths):
    """按指定 prim 路径列表计算总机器人体积"""
    total_volume = 0.0
    first_volume = 0.0
    # first_volume = compute_prim_volume(stage.GetPrimAtPath(prim_paths[0]))
    first_volume_simple = compute_prim_volume_simple(stage.GetPrimAtPath(prim_paths[0]))
    # for path in prim_paths:
    #     prim = stage.GetPrimAtPath(path)
    #     if prim and prim.IsValid():
    #         vol = compute_prim_volume(prim)
    #         print(f"[{prim.GetTypeName()}] {prim.GetPath()} | scale={get_prim_scale(prim)} => 体积={vol:.6f} m³")
    #         total_volume += vol 
    print(f"机体简化体积: {first_volume_simple:.6f} m³")
    return first_volume,total_volume
