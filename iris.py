from __future__ import annotations

import isaaclab.sim as sim_utils # 仿真工具模块
from isaaclab.actuators import ImplicitActuatorCfg  # 隐式执行器配置
from isaaclab.assets import ArticulationCfg        # 关节机器人配置

USD_PATH = "./usd_assets/iris_fov.usd"

IRIS_CFG = ArticulationCfg(
    # prim_path: 机器人在仿真环境中的路径，支持正则表达式
    prim_path="{ENV_REGEX_NS}/Robot",
    # spawn: 机器人生成相关配置
    spawn=sim_utils.UsdFileCfg(
        # usd_path: 指定usd模型文件路径
        # usd_path=f"{ISAAC_NUCLEUS_DIR}/Robots/Crazyflie/cf2x.usd",
        usd_path=f"/home/<USER>/Add_usd/iris_fov.usd",
        # rigid_props: 刚体物理属性配置
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,  # 是否关闭重力，False表示开启重力
            max_depenetration_velocity=10.0,  # 最大去穿透速度
            enable_gyroscopic_forces=True,    # 是否启用陀螺力
        ),
        # articulation_props: 关节根属性配置
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,         # 是否启用自身碰撞
            solver_position_iteration_count=4,     # 位置迭代次数
            solver_velocity_iteration_count=0,     # 速度迭代次数
            sleep_threshold=0.005,                 # 休眠阈值
            stabilization_threshold=0.001,         # 稳定化阈值
        ),
        copy_from_source=False,                    # 是否从源文件复制
        activate_contact_sensors=True              # 是否激活接触传感器
    ),
    # init_state: 初始状态配置
    # init_state=ArticulationCfg.InitialStateCfg(
    #     pos=(0.0, 0.0, 0.5),                      # 初始位置 (x, y, z)
    #     joint_pos={
    #         ".*": 0.0,                           # 所有关节初始位置为0
    #     },
    #     joint_vel={
    #         # 各电机关节的初始速度，单位通常为rad/s
    #         "m1_joint": 200.0,
    #         "m2_joint": -200.0,
    #         "m3_joint": 200.0,
    #         "m4_joint": -200.0,
    #     },
    # ),
        init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.5),                      # 初始位置 (x, y, z)
        joint_pos={
            ".*": 0.0,                           # 所有关节初始位置为0
        },
        joint_vel={
            # 各电机关节的初始速度，单位通常为rad/s
            "rotor_0_joint": 200.0,
            "rotor_3_joint": -200.0,
            "rotor_1_joint": 200.0,
            "rotor_2_joint": -200.0,
        },
    ),
    # actuators: 执行器配置
    actuators={
        # dummy: 一个示例执行器，应用于所有关节
        "dummy": ImplicitActuatorCfg(
            joint_names_expr=[".*"],  # 作用于所有关节
            stiffness=0.0,             # 刚度为0
            damping=0.0,               # 阻尼为0
        ),
    },
)