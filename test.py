# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
一个在Isaac Sim中计算并应用浮力的完整示例。
该示例演示了:
1. 如何在初始化时从USD文件中缓存网格的顶点和面数据。
2. 如何在仿真循环中实时计算物体的淹没体积和浮心。
3. 如何根据物理原理计算浮力和相应的力矩。
4. 如何将计算出的力和力矩施加到刚体上。
"""

import omni.isaac.core.utils.prims as prim_utils
from omni.isaac.core.simulation_context import SimulationContext
from omni.isaac.core.objects import FixedCuboid
from omni.isaac.core import World
from pxr import UsdGeom, Gf

import numpy as np
import torch
import trimesh

# 检查 trimesh 是否已安装
try:
    import trimesh
except ImportError:
    raise ImportError(
        "此示例需要 'trimesh' 库。请在您的 Isaac Sim Python 环境中安装: pip install trimesh"
    )

class BuoyancyExample:
    def __init__(self):
        """初始化仿真和常量"""
        # 仿真常量
        self.FLUID_DENSITY = 1000.0  # 流体密度 (kg/m^3, 水)
        self.GRAVITY_MAG = 9.81      # 重力加速度 (m/s^2)
        
        # 仿真上下文
        self.sim_context = SimulationContext(stage_units_in_meters=1.0)
        
        # 创建世界
        self.world = World(physics_dt=1.0 / 60.0, rendering_dt=1.0 / 60.0)
        
        # 将用于缓存网格数据的变量
        self.local_vertices = None
        self.local_faces = None
        
        # 浮动体对象
        self.floating_body = None
        
    def setup_scene(self):
        """设置场景：创建地面和浮动体"""
        # 创建一个地面平面作为参考
        self.world.scene.add(
            FixedCuboid(
                prim_path="/World/groundPlane",
                size=10.0,
                position=np.array([0, 0, -1.0]),
                color=np.array([0.2, 0.2, 0.2]),
            )
        )
        
        # 创建一个将要漂浮的物体 (我们使用一个圆环面)
        # prim_utils.create_prim 会创建 USD prim
        prim_utils.create_prim(
            prim_path="/World/FloatingBody",
            prim_type="Torus",
            position=np.array([0.0, 0.0, 0.5]), # 初始位置在水面之上
            attributes={"radius": 0.5, "axis": "Z", "pipeRadius": 0.15}
        )
        
        # 从创建的prim中添加一个刚体到世界中
        # 这会自动为其添加物理属性
        self.floating_body = self.world.scene.add(
            prim_utils.get_prim_at_path("/World/FloatingBody")
        )

        # 缓存网格数据
        self.cache_mesh_data()
        
        # 重置世界以确保所有对象都已加载
        self.world.reset()

    def cache_mesh_data(self):
        """
        从USD Prim中读取几何数据并缓存。
        这是在环境初始化时执行的最佳实践。
        """
        print("正在缓存网格数据...")
        
        # 获取物体的 Usd.Prim 对象
        prim = prim_utils.get_prim_at_path(self.floating_body.prim_path)
        
        # 将其转换为 UsdGeom.Mesh
        mesh_geom = UsdGeom.Mesh(prim)
        if not mesh_geom:
            raise ValueError(f"无法在路径 {self.floating_body.prim_path} 处找到有效的Mesh。")
        
        # 1. 获取顶点 (点)
        # GetPointsAttr().Get() 返回一个 Vt.Vec3fArray
        points = mesh_geom.GetPointsAttr().Get()
        if not points:
            raise ValueError("Mesh 没有顶点数据。")
        self.local_vertices = np.array(points, dtype=np.float32)
        print(f"  - 缓存了 {len(self.local_vertices)} 个顶点。")

        # 2. 获取面 (三角面索引)
        # ！！！重要: 大多数 Isaac Sim 几何体是三角化的。
        # 如果你加载的是一个包含四边形或多边形面的模型，你需要先进行三角化。
        indices = mesh_geom.GetFaceVertexIndicesAttr().Get()
        if not indices:
            raise ValueError("Mesh 没有面索引数据。")
        
        # 假设是三角面，我们将扁平的索引列表重塑为 (N, 3) 的形状
        self.local_faces = np.array(indices, dtype=np.int32).reshape(-1, 3)
        print(f"  - 缓存了 {len(self.local_faces)} 个面。")

    def calculate_and_apply_buoyancy(self):
        """在每个仿真步骤中计算并施加浮力"""
        if self.local_vertices is None or self.local_faces is None:
            return

        # --- 步骤 1: 获取当前姿态并将本地顶点转换为世界坐标 ---
        current_position, current_orientation = self.floating_body.get_world_pose()
        
        # 将缓存的本地顶点 (numpy) 转换为 PyTorch 张量以进行旋转
        vertices_tensor = torch.from_numpy(self.local_vertices).to(self.world.device)
        
        # 使用四元数旋转顶点
        rotated_vertices = self.quat_rotate(current_orientation, vertices_tensor)
        
        # 添加世界位置以完成变换
        world_vertices = rotated_vertices + current_position
        
        # 将世界坐标顶点转换回 numpy 以供 trimesh 使用
        world_vertices_np = world_vertices.cpu().numpy()

        # --- 步骤 2: 使用 Trimesh 切割网格 ---
        mesh = trimesh.Trimesh(vertices=world_vertices_np, faces=self.local_faces)
        
        # 定义水面平面 (z=0)
        water_plane_normal = np.array([0.0, 0.0, 1.0])
        water_plane_origin = np.array([0.0, 0.0, 0.0])

        try:
            # 切割网格以获得水面以下的部分
            submerged_mesh = mesh.slice_plane(
                plane_origin=water_plane_origin, 
                plane_normal=water_plane_normal
            )
        except Exception:
            # 如果物体完全在水面之上，切割可能会失败
            submerged_mesh = None
        
        is_submerged = submerged_mesh is not None and not submerged_mesh.is_empty
        
        # --- 步骤 3: 计算淹没体积和浮心 ---
        submerged_volume = 0.0
        center_of_buoyancy = None
        
        if is_submerged:
            submerged_volume = submerged_mesh.volume
            center_of_buoyancy = submerged_mesh.centroid

        # --- 步骤 4: 计算浮力并施加 ---
        if submerged_volume > 0.01: # 添加一个小的阈值以避免数值不稳定
            # 计算浮力大小
            buoyancy_force_mag = submerged_volume * self.FLUID_DENSITY * self.GRAVITY_MAG
            
            # 浮力向量 (方向与重力相反)
            buoyancy_force_vec = torch.tensor([0.0, 0.0, buoyancy_force_mag], device=self.world.device)
            
            # 手动计算力矩 torque = r x F
            # r 是从质心到浮心的向量
            com_position, _ = self.floating_body.get_com()
            r = torch.from_numpy(center_of_buoyancy).to(self.world.device) - com_position
            torque_vec = torch.cross(r, buoyancy_force_vec)
            
            # 施加力和力矩
            self.floating_body.apply_force(buoyancy_force_vec)
            self.floating_body.apply_torque(torque_vec)
            
            print(f"淹没体积: {submerged_volume:.4f} m^3, 浮力: {buoyancy_force_mag:.2f} N, 力矩: {torque_vec.cpu().numpy()}")
        else:
            print("物体在水面之上，无浮力。")

    def run_simulation(self):
        """运行主仿真循环"""
        self.setup_scene()
        
        # 运行仿真
        for i in range(500): # 运行500帧
            # 计算浮力是仿真逻辑的一部分，在 world.step 之前调用
            self.calculate_and_apply_buoyancy()
            
            # 运行一步仿真
            self.world.step(render=True)
        
        # 关闭仿真
        self.sim_context.stop()

    # 辅助函数: 使用 PyTorch 实现四元数旋转
    def quat_rotate(self, q, v):
        """使用四元数 q 旋转向量 v"""
        q_w = q[0]
        q_xyz = q[1:]
        t = 2.0 * torch.cross(q_xyz, v)
        v_prime = v + q_w * t + torch.cross(q_xyz, t)
        return v_prime

if __name__ == "__main__":
    example = BuoyancyExample()
    example.run_simulation()