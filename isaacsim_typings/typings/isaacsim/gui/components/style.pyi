from __future__ import annotations
import carb as carb
from omni.kit.window.extensions.common import get_icons_path
from omni import ui
__all__ = ['BUTTON_WIDTH', 'COLOR_W', 'COLOR_X', 'COLOR_Y', 'COLOR_Z', 'HORIZONTAL_SPACING', '<PERSON>BE<PERSON>_WIDTH', 'VERTICAL_SPACING', 'carb', 'get_icons_path', 'get_style', 'ui']
def get_style():
    ...
BUTTON_WIDTH: int = 120
COLOR_W: int = 4289353045
COLOR_X: int = 4283782570
COLOR_Y: int = 4285965169
COLOR_Z: int = 4288707919
HORIZONTAL_SPACING: int = 4
LABEL_WIDTH: int = 120
VERTICAL_SPACING: int = 5
