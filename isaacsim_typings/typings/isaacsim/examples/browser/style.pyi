from __future__ import annotations
import omni.ui.color_utils
__all__ = ['CONTEXT_MENU_STYLE', 'cl']
CONTEXT_MENU_STYLE: dict  # value = {'Menu': {'background_color': 'context_menu_background_color', 'color': 'context_menu_text', 'border_radius': 2}, 'Menu.Item': {'background_color': 0, 'margin': 0}, 'Separator': {'background_color': 0, 'color': 'context_menu_separator'}}
cl: omni.ui.color_utils.ColorShade  # value = <omni.ui.color_utils.ColorShade object>
