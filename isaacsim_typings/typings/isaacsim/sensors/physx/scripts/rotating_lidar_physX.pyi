from __future__ import annotations
import carb as carb
import isaacsim.core.api.sensors.base_sensor
from isaacsim.core.api.sensors.base_sensor import BaseSensor
from isaacsim.core.utils.prims import get_prim_at_path
from isaacsim.core.utils.prims import is_prim_path_valid
from isaacsim.core.utils.stage import get_current_stage
from isaacsim.sensors.physx import _range_sensor
import numpy as np
import omni as omni
from omni.isaac import RangeSensorSchema
from pxr import Sdf
__all__ = ['BaseSensor', 'RangeSensorSchema', 'RotatingLidarPhysX', 'Sdf', 'carb', 'get_current_stage', 'get_prim_at_path', 'is_prim_path_valid', 'np', 'omni']
class RotatingLidarPhysX(isaacsim.core.api.sensors.base_sensor.BaseSensor):
    def __init__(self, prim_path: str, name: str = 'rotating_lidar_physX', rotation_frequency: typing.Optional[float] = None, rotation_dt: typing.Optional[float] = None, position: typing.Optional[numpy.ndarray] = None, translation: typing.Optional[numpy.ndarray] = None, orientation: typing.Optional[numpy.ndarray] = None, fov: typing.Optional[typing.Tuple[float, float]] = None, resolution: typing.Optional[typing.Tuple[float, float]] = None, valid_range: typing.Optional[typing.Tuple[float, float]] = None) -> None:
        ...
    def _data_acquisition_callback(self, step_size: float) -> None:
        ...
    def _stage_open_callback_fn(self, event):
        ...
    def _timeline_timer_callback_fn(self, event):
        ...
    def add_azimuth_data_to_frame(self) -> None:
        ...
    def add_depth_data_to_frame(self) -> None:
        ...
    def add_intensity_data_to_frame(self) -> None:
        ...
    def add_linear_depth_data_to_frame(self) -> None:
        ...
    def add_point_cloud_data_to_frame(self) -> None:
        ...
    def add_semantics_data_to_frame(self) -> None:
        ...
    def add_zenith_data_to_frame(self) -> None:
        ...
    def disable_semantics(self) -> None:
        ...
    def disable_visualization(self) -> None:
        ...
    def enable_semantics(self) -> None:
        ...
    def enable_visualization(self, high_lod: bool = False, draw_points: bool = True, draw_lines: bool = True) -> None:
        ...
    def get_current_frame(self) -> dict:
        ...
    def get_fov(self) -> typing.Tuple[float, float]:
        ...
    def get_num_cols(self) -> int:
        ...
    def get_num_cols_in_last_step(self) -> int:
        ...
    def get_num_rows(self) -> int:
        ...
    def get_resolution(self) -> float:
        ...
    def get_rotation_frequency(self) -> int:
        ...
    def get_valid_range(self) -> typing.Tuple[float, float]:
        ...
    def initialize(self, physics_sim_view = None) -> None:
        ...
    def is_paused(self) -> bool:
        ...
    def is_semantics_enabled(self) -> bool:
        ...
    def pause(self) -> None:
        ...
    def post_reset(self) -> None:
        ...
    def remove_azimuth_data_from_frame(self) -> None:
        ...
    def remove_depth_data_from_frame(self) -> None:
        ...
    def remove_intensity_data_from_frame(self) -> None:
        ...
    def remove_linear_depth_data_from_frame(self) -> None:
        ...
    def remove_point_cloud_data_from_frame(self) -> None:
        ...
    def remove_semantics_data_from_frame(self) -> None:
        ...
    def remove_zenith_data_from_frame(self) -> None:
        ...
    def resume(self) -> None:
        ...
    def set_fov(self, value: typing.Tuple[float, float]) -> None:
        ...
    def set_resolution(self, value: float) -> None:
        ...
    def set_rotation_frequency(self, value: int) -> None:
        ...
    def set_valid_range(self, value: typing.Tuple[float, float]) -> None:
        ...
