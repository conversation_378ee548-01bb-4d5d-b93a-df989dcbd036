from __future__ import annotations
import carb as carb
import copy as copy
from isaacsim.core.nodes.bindings import _isaacsim_core_nodes
import isaacsim.core.prims.impl.single_articulation
from isaacsim.core.prims.impl.single_articulation import SingleArticulation
import numpy as np
import omni as omni
__all__ = ['EffortSensor', 'EsSensorReading', 'SingleArticulation', 'carb', 'copy', 'np', 'omni']
class EffortSensor(isaacsim.core.prims.impl.single_articulation.SingleArticulation):
    def __init__(self, prim_path: str, sensor_period: float = -1, use_latest_data: bool = False, enabled: bool = True) -> None:
        ...
    def _data_acquisition_callback(self, step_size: float) -> None:
        ...
    def _stage_open_callback_fn(self, event = None) -> None:
        ...
    def _timeline_timer_callback_fn(self, event) -> None:
        ...
    def change_buffer_size(self, new_buffer_size: int) -> None:
        ...
    def get_sensor_reading(self, interpolation_function = None, use_latest_data = False) -> <isaacsim.sensors.physics.scripts.effort_sensor.EsSensorReading object>:
        ...
    def initialize_callbacks(self) -> None:
        ...
    def lerp(self, start: float, end: float, time: float) -> float:
        ...
    def update_dof_name(self, dof_name: str) -> None:
        ...
class EsSensorReading:
    def __init__(self, is_valid: bool = False, time: float = 0, value: float = 0) -> None:
        ...
