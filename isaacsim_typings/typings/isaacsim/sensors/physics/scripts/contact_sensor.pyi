from __future__ import annotations
import carb as carb
import isaacsim.core.api.sensors.base_sensor
from isaacsim.core.api.sensors.base_sensor import BaseSensor
from isaacsim.core.nodes.bindings import _isaacsim_core_nodes
from isaacsim.core.utils.prims import get_prim_at_path
from isaacsim.core.utils.prims import is_prim_path_valid
from isaacsim.core.utils.stage import traverse_stage
from isaacsim.sensors.physics import _sensor
import numpy as np
import omni as omni
from omni.isaac import IsaacSensorSchema
from pxr import Gf
from pxr import PhysxSchema
from pxr import UsdPhysics
__all__ = ['BaseSensor', 'ContactSensor', 'Gf', 'IsaacSensorSchema', 'PhysxSchema', 'UsdPhysics', 'carb', 'get_prim_at_path', 'is_prim_path_valid', 'np', 'omni', 'traverse_stage']
class ContactSensor(isaacsim.core.api.sensors.base_sensor.BaseSensor):
    def __init__(self, prim_path: str, name: typing.Optional[str] = 'contact_sensor', frequency: typing.Optional[int] = None, dt: typing.Optional[float] = None, translation: typing.Optional[numpy.ndarray] = None, position: typing.Optional[numpy.ndarray] = None, min_threshold: typing.Optional[float] = None, max_threshold: typing.Optional[float] = None, radius: typing.Optional[float] = None) -> None:
        ...
    def add_raw_contact_data_to_frame(self) -> None:
        ...
    def get_current_frame(self) -> None:
        ...
    def get_dt(self) -> float:
        ...
    def get_frequency(self) -> int:
        ...
    def get_max_threshold(self) -> float:
        ...
    def get_min_threshold(self) -> float:
        ...
    def get_radius(self) -> float:
        ...
    def initialize(self, physics_sim_view = None) -> None:
        ...
    def is_paused(self) -> bool:
        ...
    def pause(self) -> None:
        ...
    def remove_raw_contact_data_from_frame(self) -> None:
        ...
    def resume(self) -> None:
        ...
    def set_dt(self, value: float) -> None:
        ...
    def set_frequency(self, value: float) -> None:
        ...
    def set_max_threshold(self, value: float) -> None:
        ...
    def set_min_threshold(self, value: float) -> None:
        ...
    def set_radius(self, value: float) -> None:
        ...
