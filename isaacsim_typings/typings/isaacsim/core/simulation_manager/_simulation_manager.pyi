"""
pybind11 isaacsim.core.simulation_manager.pybind bindings
"""
from __future__ import annotations
import typing
__all__ = ['ISimulationManager', 'acquire_simulation_manager_interface', 'release_simulation_manager_interface']
class ISimulationManager:
    def deregister_callback(self, arg0: int) -> bool:
        ...
    def enable_fabric_usd_notice_handler(self, arg0: int, arg1: bool) -> None:
        ...
    def enable_usd_notice_handler(self, arg0: bool) -> None:
        ...
    def get_callback_iter(self) -> int:
        ...
    def is_fabric_usd_notice_handler_enabled(self, arg0: int) -> bool:
        ...
    def register_deletion_callback(self, arg0: typing.Callable[[str], None]) -> int:
        ...
    def register_physics_scene_addition_callback(self, arg0: typing.Callable[[str], None]) -> int:
        ...
    def reset(self) -> None:
        ...
    def set_callback_iter(self, arg0: int) -> None:
        ...
def acquire_simulation_manager_interface(plugin_name: str = None, library_path: str = None) -> ISimulationManager:
    ...
def release_simulation_manager_interface(arg0: ISimulationManager) -> None:
    ...
