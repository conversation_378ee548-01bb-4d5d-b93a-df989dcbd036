"""
pybind11 isaacsim.core.nodes bindings
"""
from __future__ import annotations
__all__ = ['CoreNodes', 'acquire_interface', 'release_interface']
class CoreNodes:
    def get_physics_num_steps(self) -> int:
        ...
    def get_sim_time(self) -> float:
        ...
    def get_sim_time_at_swh_frame(self, arg0: int) -> float:
        ...
    def get_sim_time_at_time(self, arg0: ...) -> float:
        ...
    def get_sim_time_monotonic(self) -> float:
        ...
    def get_sim_time_monotonic_at_swh_frame(self, arg0: int) -> float:
        ...
    def get_sim_time_monotonic_at_time(self, arg0: ...) -> float:
        ...
    def get_system_time(self) -> float:
        ...
    def get_system_time_at_swh_frame(self, arg0: int) -> float:
        ...
    def get_system_time_at_time(self, arg0: ...) -> float:
        ...
def acquire_interface(plugin_name: str = None, library_path: str = None) -> CoreNodes:
    ...
def release_interface(arg0: CoreNodes) -> None:
    ...
