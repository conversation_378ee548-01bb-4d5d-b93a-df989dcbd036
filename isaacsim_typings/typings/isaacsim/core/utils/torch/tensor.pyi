from __future__ import annotations
import torch as torch
__all__ = ['as_type', 'assign', 'clone_tensor', 'convert', 'create_tensor_from_list', 'create_zeros_tensor', 'expand_dims', 'move_data', 'pad', 'resolve_indices', 'tensor_cat', 'tensor_stack', 'to_list', 'to_numpy', 'torch']
def as_type(data, dtype):
    ...
def assign(src, dst, indices):
    ...
def clone_tensor(data, device):
    ...
def convert(data, device, dtype = 'float32', indexed = None):
    ...
def create_tensor_from_list(data, dtype, device = None):
    ...
def create_zeros_tensor(shape, dtype, device = None):
    ...
def expand_dims(data, axis):
    ...
def move_data(data, device):
    ...
def pad(data, pad_width, mode = 'constant', value = None):
    ...
def resolve_indices(indices, count, device):
    ...
def tensor_cat(data, device = None, dim = -1):
    ...
def tensor_stack(data, dim = 0):
    ...
def to_list(data):
    ...
def to_numpy(data):
    ...
