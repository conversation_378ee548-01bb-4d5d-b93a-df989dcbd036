from PIL import Image
from PIL import ImageDraw
from __future__ import annotations
import carb as carb
from functools import partial
import io as io
from isaacsim.core.utils.mesh import get_mesh_vertices_relative_to
from isaacsim.replicator.writers.scripts.utils import NumpyEncoder
from isaacsim.replicator.writers.scripts.utils import calculate_truncation_ratio_simple
from isaacsim.replicator.writers.scripts.writers.data_visualization_writer import DataVisualizationWriter
from isaacsim.replicator.writers.scripts.writers.dope_writer import DOPEWriter
from isaacsim.replicator.writers.scripts.writers.pose_writer import PoseWriter
from isaacsim.replicator.writers.scripts.writers.pytorch_listener import PytorchListener
from isaacsim.replicator.writers.scripts.writers.pytorch_writer import PytorchWriter
from isaacsim.replicator.writers.scripts.writers.ycb_video_writer import YCBVideoWriter
import json as json
import numpy as np
from omni.replicator.core.scripts.annotators import AnnotatorRegistry
from omni.replicator.core.scripts.backends.dispatcher import BackendDispatch
from omni.replicator.core.scripts.functional.io_functions import write_image
from omni.replicator.core.scripts.functional.io_functions import write_json
from omni.replicator.core.scripts.writers import Writer
from omni.replicator.core.scripts.writers import WriterRegistry
from omni.syntheticdata.scripts.SyntheticData import SyntheticData
from omni.syntheticdata.scripts.SyntheticData.SyntheticData import NodeConnectionTemplate
from omni.syntheticdata.scripts.SyntheticData.SyntheticData import NodeTemplate
import os as os
from pxr import Gf
from pxr import Usd
from pxr import UsdGeom
from scipy.io.matlab._mio import savemat
import torch as torch
import warp as wp
from . import data_visualization_writer
from . import dope_writer
from . import pose_writer
from . import pytorch_listener
from . import pytorch_writer
from . import ycb_video_writer
__all__ = ['AnnotatorRegistry', 'BackendDispatch', 'DOPEWriter', 'DataVisualizationWriter', 'Gf', 'Image', 'ImageDraw', 'NodeConnectionTemplate', 'NodeTemplate', 'NumpyEncoder', 'PoseWriter', 'PytorchListener', 'PytorchWriter', 'SyntheticData', 'Usd', 'UsdGeom', 'Writer', 'WriterRegistry', 'YCBVideoWriter', 'calculate_truncation_ratio_simple', 'carb', 'data_visualization_writer', 'dope_writer', 'get_mesh_vertices_relative_to', 'io', 'json', 'np', 'os', 'partial', 'pose_writer', 'pytorch_listener', 'pytorch_writer', 'register_writers', 'savemat', 'torch', 'wp', 'write_image', 'write_json', 'ycb_video_writer']
def register_writers():
    ...
