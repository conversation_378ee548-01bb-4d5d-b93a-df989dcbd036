from __future__ import annotations
from omni.graph import core as og
from omni.replicator.core.utils import utils
__all__ = ['ReplicatorIsaacContext', 'get_reset_inds', 'initialize_context', 'og', 'trigger_randomization', 'utils']
class ReplicatorIsaacContext:
    def __init__(self, num_envs, action_graph_entry_node):
        ...
    def add_tendon_exec_context(self, node):
        ...
    def get_tendon_exec_context(self):
        ...
    def trigger_randomization(self, reset_inds):
        ...
    @property
    def reset_inds(self):
        ...
def get_reset_inds():
    ...
def initialize_context(num_envs, action_graph_entry_node):
    ...
def trigger_randomization(reset_inds):
    ...
_context = None
