from __future__ import annotations
from omni import ui
__all__ = ['ResetButton', 'ResetableLabelField', 'ui']
class ResetButton:
    enable = ...
    def __init__(self, init_value, on_reset_fn):
        ...
    def _build_ui(self):
        ...
    def _restore_defaults(self):
        ...
    def refresh(self, new_value):
        ...
class ResetableLabelField:
    enabled = ...
    visible = ...
    def __init__(self, value_model, field_type, format, alignment = ...):
        ...
    def _begin_edit(self):
        ...
    def _build_ui(self):
        ...
    def _end_edit(self, model):
        ...
    def _on_reset_fn(self):
        ...
    def _update_field(self, model):
        ...
    def _update_value(self, model):
        ...
    def get_model_value(self, model):
        ...
    def update_default_value(self):
        ...
