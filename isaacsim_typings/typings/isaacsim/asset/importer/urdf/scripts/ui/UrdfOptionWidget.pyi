from __future__ import annotations
from isaacsim.asset.importer.urdf.scripts.ui.UrdfJointWidgetWithID import JointSettingMode
from isaacsim.asset.importer.urdf.scripts.ui.UrdfJointWidgetWithID import UrdfJointWidget
from isaacsim.asset.importer.urdf.scripts.ui.style import get_option_style
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import add_folder_picker_icon
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import checkbox_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import float_field_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import format_tt
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import string_filed_builder
from omni import ui
__all__ = ['JointSettingMode', 'UrdfJointWidget', 'UrdfOptionWidget', 'add_folder_picker_icon', 'checkbox_builder', 'float_field_builder', 'format_tt', 'get_option_style', 'option_frame', 'option_header', 'string_filed_builder', 'ui']
class UrdfOptionWidget:
    def __init__(self, models, config):
        ...
    def _build_colliders_frame(self):
        ...
    def _build_joint_drive_frame(self):
        ...
    def _build_links_frame(self):
        ...
    def _build_model_frame(self):
        ...
    def _parse_mimic(self, value):
        ...
    def _set_bulk_edit(self, enable):
        ...
    def _switch_drive_type(self, switch):
        ...
    def _switch_mode(self, switch):
        ...
    def _update_convex_decomp(self, model):
        ...
    def _update_fix_base(self, model):
        ...
    def _update_import_option(self, model):
        ...
    def build_joint_widget(self, urdf_robot, joints_values, on_joint_changed):
        ...
    def build_options(self):
        ...
    @property
    def config(self):
        ...
    @property
    def models(self):
        ...
def option_frame(title, build_content_fn, collapse_fn = None):
    ...
def option_header(collapsed, title):
    ...
