from __future__ import annotations
import asyncio as asyncio
import carb as carb
from isaacsim.asset.importer.urdf.scripts.ui.menu import make_menu_item_description
from isaacsim.asset.importer.urdf.scripts.ui.style import get_option_style
from isaacsim.asset.importer.urdf.scripts.ui.style import get_style
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import SearchListItem
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import SearchListItemDelegate
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import SearchListItemModel
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import add_folder_picker_btn
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import add_folder_picker_icon
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import add_line_rect_flourish
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import add_separator
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import btn_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import build_header
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import build_info_frame
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import build_simple_search
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import cb_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import checkbox_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import color_picker_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_cb_dropdown_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_cb_plot_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_cb_scrolling_frame_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_cb_str_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_cb_xyz_plot_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_floatfield_slider_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import combo_intfield_slider_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import dropdown_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import float_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import float_field_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import format_tt
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import int_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import multi_btn_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import multi_cb_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import multi_dropdown_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import on_copy_to_clipboard
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import on_docs_link_clicked
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import on_open_IDE_clicked
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import on_open_folder_clicked
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import plot_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import progress_bar_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import scrolling_frame_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import setup_ui_headers
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import state_btn_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import str_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import string_filed_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import xyz_builder
from isaacsim.asset.importer.urdf.scripts.ui.ui_utils import xyz_plot_builder
import omni as omni
from omni.kit.menu.utils.builder_utils import MenuItemDescription
from omni.kit.window.extensions.common import get_icons_path
from omni.kit.window.extensions.ext_components import SimpleCheckBox
from omni.kit.window.filepicker.dialog import FilePickerDialog
from omni import ui
import os as os
import pathlib as pathlib
import subprocess as subprocess
import sys as sys
from . import UrdfJointWidgetWithID
from . import UrdfOptionWidget
from . import menu
from . import resetable_widget
from . import style
from . import ui_utils
__all__ = ['BUTTON_BG_COLOR', 'BUTTON_WIDTH', 'COLOR_W', 'COLOR_X', 'COLOR_Y', 'COLOR_Z', 'DISABLED_LABEL_COLOR', 'EXTENSION_FOLDER_PATH', 'FONT_SIZE', 'FRAME_BG_COLOR', 'FRAME_HEAD_COLOR', 'FilePickerDialog', 'HEADER_FONT_SIZE', 'HORIZONTAL_SPACING', 'LABEL_COLOR', 'LABEL_HEIGHT', 'LABEL_TITLE_COLOR', 'LABEL_WIDTH', 'LINE_COLOR', 'MenuItemDescription', 'STRING_FIELD_LABEL_COLOR', 'SearchListItem', 'SearchListItemDelegate', 'SearchListItemModel', 'SimpleCheckBox', 'TREEVIEW_BG_COLOR', 'TREEVIEW_HEADER_BG_COLOR', 'TREEVIEW_ITEM_COLOR', 'TREEVIEW_ITEM_FONT', 'TREEVIEW_SELECTED_COLOR', 'TRIANGLE_COLOR', 'UNIT_COLOR', 'UrdfJointWidgetWithID', 'UrdfOptionWidget', 'VERTICAL_SPACING', 'add_folder_picker_btn', 'add_folder_picker_icon', 'add_line_rect_flourish', 'add_separator', 'asyncio', 'btn_builder', 'build_header', 'build_info_frame', 'build_simple_search', 'carb', 'cb_builder', 'checkbox_builder', 'cl', 'color_picker_builder', 'combo_cb_dropdown_builder', 'combo_cb_plot_builder', 'combo_cb_scrolling_frame_builder', 'combo_cb_str_builder', 'combo_cb_xyz_plot_builder', 'combo_floatfield_slider_builder', 'combo_intfield_slider_builder', 'dropdown_builder', 'float_builder', 'float_field_builder', 'format_tt', 'get_icons_path', 'get_option_style', 'get_style', 'inf', 'int_builder', 'make_menu_item_description', 'menu', 'multi_btn_builder', 'multi_cb_builder', 'multi_dropdown_builder', 'omni', 'on_copy_to_clipboard', 'on_docs_link_clicked', 'on_open_IDE_clicked', 'on_open_folder_clicked', 'os', 'pathlib', 'plot_builder', 'progress_bar_builder', 'resetable_widget', 'scrolling_frame_builder', 'setup_ui_headers', 'state_btn_builder', 'str_builder', 'string_filed_builder', 'style', 'subprocess', 'sys', 'ui', 'ui_utils', 'xyz_builder', 'xyz_plot_builder']
BUTTON_BG_COLOR: int = 4280557855
BUTTON_WIDTH: int = 120
COLOR_W: int = 4289353045
COLOR_X: int = 4283782570
COLOR_Y: int = 4285965169
COLOR_Z: int = 4288707919
DISABLED_LABEL_COLOR: int = 4285427310
EXTENSION_FOLDER_PATH: pathlib.PosixPath  # value = PosixPath('/home/<USER>/isaacsim/extscache/isaacsim.asset.importer.urdf-2.3.10+106.4.0.lx64.r.cp310')
FONT_SIZE: int = 14
FRAME_BG_COLOR: int = 4281611315
FRAME_HEAD_COLOR: int = 4287598479
HEADER_FONT_SIZE: int = 16
HORIZONTAL_SPACING: int = 4
LABEL_COLOR: int = 4292401368
LABEL_HEIGHT: int = 18
LABEL_TITLE_COLOR: int = 4291611852
LABEL_WIDTH: int = 160
LINE_COLOR: int = 4287598479
STRING_FIELD_LABEL_COLOR: int = 4287598479
TREEVIEW_BG_COLOR: int = 4280492319
TREEVIEW_HEADER_BG_COLOR: int = 4281150765
TREEVIEW_ITEM_COLOR: int = 4281611314
TREEVIEW_ITEM_FONT: int = 14
TREEVIEW_SELECTED_COLOR: int = 4283124290
TRIANGLE_COLOR: int = 4287598479
UNIT_COLOR: int = 4285427310
VERTICAL_SPACING: int = 5
cl: omni.ui.color_utils.ColorShade  # value = <omni.ui.color_utils.ColorShade object>
inf: float  # value = inf
