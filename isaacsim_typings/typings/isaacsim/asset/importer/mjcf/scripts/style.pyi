from __future__ import annotations
import carb as carb
import omni as omni
from omni.kit.window.extensions.common import get_icons_path
from omni import ui
import pathlib as pathlib
__all__ = ['BUTTON_BG_COLOR', 'BUTTON_WIDTH', 'COLOR_W', 'COLOR_X', 'COLOR_Y', 'COLOR_Z', 'DISABLED_LABEL_COLOR', 'EXTENSION_FOLDER_PATH', 'FONT_SIZE', 'FRAME_BG_COLOR', 'FRAME_HEAD_COLOR', 'HEADER_FONT_SIZE', 'HORIZONTAL_SPACING', 'LABEL_COLOR', 'LA<PERSON>L_TITLE_COLOR', '<PERSON>BEL_WIDTH', 'LINE_COLOR', 'STRING_FIELD_LABEL_COLOR', 'TREEVIEW_ITEM_FONT', 'TRIANGLE_COLOR', 'UNIT_COLOR', 'VERTICAL_SPACING', 'carb', 'cl', 'get_icons_path', 'get_option_style', 'get_style', 'omni', 'pathlib', 'ui']
def get_option_style():
    ...
def get_style():
    ...
BUTTON_BG_COLOR: int = 4280557855
BUTTON_WIDTH: int = 120
COLOR_W: int = 4289353045
COLOR_X: int = 4283782570
COLOR_Y: int = 4285965169
COLOR_Z: int = 4288707919
DISABLED_LABEL_COLOR: int = 4285427310
EXTENSION_FOLDER_PATH: pathlib.PosixPath  # value = PosixPath('/home/<USER>/isaacsim/extscache/isaacsim.asset.importer.mjcf-2.3.3+106.3.0.lx64.r.cp310')
FONT_SIZE: int = 14
FRAME_BG_COLOR: int = 4281611315
FRAME_HEAD_COLOR: int = 4287598479
HEADER_FONT_SIZE: int = 16
HORIZONTAL_SPACING: int = 4
LABEL_COLOR: int = 4292401368
LABEL_TITLE_COLOR: int = 4291611852
LABEL_WIDTH: int = 120
LINE_COLOR: int = 4287598479
STRING_FIELD_LABEL_COLOR: int = 4287598479
TREEVIEW_ITEM_FONT: int = 14
TRIANGLE_COLOR: int = 4287598479
UNIT_COLOR: int = 4285427310
VERTICAL_SPACING: int = 5
cl: omni.ui.color_utils.ColorShade  # value = <omni.ui.color_utils.ColorShade object>
