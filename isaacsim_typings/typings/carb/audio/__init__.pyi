from __future__ import annotations
from carb.audio._audio import AudioResult
from carb.audio._audio import Context
from carb.audio._audio import ContextCaps
from carb.audio._audio import ContextError
from carb.audio._audio import ContextParams
from carb.audio._audio import ContextParams2
from carb.audio._audio import DecoderError
from carb.audio._audio import DeviceCaps
from carb.audio._audio import DspValuePair
from carb.audio._audio import EmitterAttributes
from carb.audio._audio import EncoderError
from carb.audio._audio import EntityAttributes
from carb.audio._audio import EntityCone
from carb.audio._audio import EventPoint
from carb.audio._audio import Float2
from carb.audio._audio import Guid
from carb.audio._audio import IAudioData
from carb.audio._audio import IAudioPlayback
from carb.audio._audio import LoopPointDesc
from carb.audio._audio import NotAvailable
from carb.audio._audio import OutputDesc
from carb.audio._audio import PeakVolumes
from carb.audio._audio import PlaybackContextDesc
from carb.audio._audio import ReadOnly
from carb.audio._audio import RolloffDesc
from carb.audio._audio import RolloffType
from carb.audio._audio import SampleFormat
from carb.audio._audio import SoundData
from carb.audio._audio import SoundDataError
from carb.audio._audio import SoundFormat
from carb.audio._audio import Speaker
from carb.audio._audio import UnitType
from carb.audio._audio import Voice
from carb.audio._audio import VoiceParamBalance
from carb.audio._audio import VoiceParamOcclusion
from carb.audio._audio import VoiceParams
from carb.audio._audio import acquire_data_interface
from carb.audio._audio import acquire_playback_interface
from carb.audio._audio import get_force_off_playback_mode_flags
from carb.audio._audio import get_force_on_playback_mode_flags
from . import _audio
__all__ = ['AUDIO_IMAGE_FLAG_ALPHA_BLEND', 'AUDIO_IMAGE_FLAG_MULTI_CHANNEL', 'AUDIO_IMAGE_FLAG_NOISE_COLOR', 'AUDIO_IMAGE_FLAG_SPLIT_CHANNELS', 'AUDIO_IMAGE_FLAG_USE_LINES', 'AudioResult', 'CONE_ANGLE_OMNI_DIRECTIONAL', 'CONTEXT_FLAG_BAKING', 'CONTEXT_FLAG_MANUAL_STOP', 'CONTEXT_PARAM_ALL', 'CONTEXT_PARAM_DEFAULT_PLAYBACK_MODE', 'CONTEXT_PARAM_DOPPLER_LIMIT', 'CONTEXT_PARAM_DOPPLER_SCALE', 'CONTEXT_PARAM_LISTENER', 'CONTEXT_PARAM_MASTER_VOLUME', 'CONTEXT_PARAM_NON_SPATIAL_FREQUENCY_RATIO', 'CONTEXT_PARAM_NON_SPATIAL_VOLUME', 'CONTEXT_PARAM_SPATIAL_FREQUENCY_RATIO', 'CONTEXT_PARAM_SPATIAL_VOLUME', 'CONTEXT_PARAM_SPEED_OF_SOUND', 'CONTEXT_PARAM_VIDEO_LATENCY', 'CONTEXT_PARAM_VIRTUALIZATION_THRESHOLD', 'CONTEXT_PARAM_WORLD_UNIT_SCALE', 'Context', 'ContextCaps', 'ContextError', 'ContextParams', 'ContextParams2', 'DATA_FLAG_CALC_PEAKS', 'DATA_FLAG_SKIP_EVENT_POINTS', 'DATA_FLAG_SKIP_METADATA', 'DEFAULT_CHANNEL_COUNT', 'DEFAULT_FORMAT', 'DEFAULT_FRAME_RATE', 'DEFAULT_SPEED_OF_SOUND', 'DEVICE_FLAG_CONNECTED', 'DEVICE_FLAG_DEFAULT', 'DEVICE_FLAG_NOT_OPEN', 'DEVICE_FLAG_STREAMER', 'DecoderError', 'DeviceCaps', 'DspValuePair', 'ENTITY_FLAG_ALL', 'ENTITY_FLAG_CONE', 'ENTITY_FLAG_FORWARD', 'ENTITY_FLAG_MAKE_PERP', 'ENTITY_FLAG_NORMALIZE', 'ENTITY_FLAG_POSITION', 'ENTITY_FLAG_ROLLOFF', 'ENTITY_FLAG_UP', 'ENTITY_FLAG_VELOCITY', 'EVENT_POINT_INVALID_FRAME', 'EVENT_POINT_LOOP_INFINITE', 'EmitterAttributes', 'EncoderError', 'EntityAttributes', 'EntityCone', 'EventPoint', 'Float2', 'Guid', 'IAudioData', 'IAudioPlayback', 'INSTANCES_UNLIMITED', 'INVALID_DEVICE_INDEX', 'INVALID_SPEAKER_NAME', 'LoopPointDesc', 'MAX_CHANNELS', 'MAX_FRAMERATE', 'MAX_NAME_LENGTH', 'MEMORY_LIMIT_THRESHOLD', 'META_DATA_TAG_ALBUM', 'META_DATA_TAG_ARCHIVAL_LOCATION', 'META_DATA_TAG_ARTIST', 'META_DATA_TAG_AUDIO_SOURCE_WEBSITE', 'META_DATA_TAG_BPM', 'META_DATA_TAG_CLEAR_ALL_TAGS', 'META_DATA_TAG_COMMENT', 'META_DATA_TAG_COMMISSIONED', 'META_DATA_TAG_COMPOSER', 'META_DATA_TAG_CONTACT', 'META_DATA_TAG_COPYRIGHT', 'META_DATA_TAG_CREATION_DATE', 'META_DATA_TAG_CROPPED', 'META_DATA_TAG_DESCRIPTION', 'META_DATA_TAG_DIMENSIONS', 'META_DATA_TAG_DISC', 'META_DATA_TAG_DPI', 'META_DATA_TAG_EDITOR', 'META_DATA_TAG_ENCODER', 'META_DATA_TAG_END_TIME', 'META_DATA_TAG_ENGINEER', 'META_DATA_TAG_FILE_NAME', 'META_DATA_TAG_GENRE', 'META_DATA_TAG_INITIAL_KEY', 'META_DATA_TAG_INTERNET_ARTIST_WEBSITE', 'META_DATA_TAG_INTERNET_COMMERCIAL_INFORMATION_URL', 'META_DATA_TAG_INTERNET_COPYRIGHT_URL', 'META_DATA_TAG_INTERNET_RADIO_STATION_NAME', 'META_DATA_TAG_INTERNET_RADIO_STATION_OWNER', 'META_DATA_TAG_INTERNET_RADIO_STATION_URL', 'META_DATA_TAG_ISRC', 'META_DATA_TAG_KEYWORDS', 'META_DATA_TAG_LANGUAGE', 'META_DATA_TAG_LICENSE', 'META_DATA_TAG_LIGHTNESS', 'META_DATA_TAG_LOCATION', 'META_DATA_TAG_MEDIUM', 'META_DATA_TAG_ORGANIZATION', 'META_DATA_TAG_ORIGINAL_YEAR', 'META_DATA_TAG_OWNER', 'META_DATA_TAG_PALETTE_SETTING', 'META_DATA_TAG_PAYMENT_URL', 'META_DATA_TAG_PERFORMER', 'META_DATA_TAG_PLAYLIST_DELAY', 'META_DATA_TAG_PUBLISHER', 'META_DATA_TAG_RECORDING_DATE', 'META_DATA_TAG_SHARPNESS', 'META_DATA_TAG_SOURCE_FORM', 'META_DATA_TAG_SPEED', 'META_DATA_TAG_START_TIME', 'META_DATA_TAG_SUBGENRE', 'META_DATA_TAG_SUBJECT', 'META_DATA_TAG_TECHNICIAN', 'META_DATA_TAG_TERMS_OF_USE', 'META_DATA_TAG_TITLE', 'META_DATA_TAG_TRACK_NUMBER', 'META_DATA_TAG_VERSION', 'META_DATA_TAG_WEBSITE', 'META_DATA_TAG_WRITER', 'MIN_CHANNELS', 'MIN_FRAMERATE', 'NotAvailable', 'OUTPUT_FLAG_DEVICE', 'OutputDesc', 'PLAYBACK_MODE_DEFAULT_DISTANCE_DELAY', 'PLAYBACK_MODE_DEFAULT_INTERAURAL_DELAY', 'PLAYBACK_MODE_DEFAULT_USE_DOPPLER', 'PLAYBACK_MODE_DEFAULT_USE_FILTERS', 'PLAYBACK_MODE_DEFAULT_USE_REVERB', 'PLAYBACK_MODE_DISTANCE_DELAY', 'PLAYBACK_MODE_FADE_IN', 'PLAYBACK_MODE_INTERAURAL_DELAY', 'PLAYBACK_MODE_LISTENER_RELATIVE', 'PLAYBACK_MODE_MUTED', 'PLAYBACK_MODE_NO_POSITION_SIMULATION', 'PLAYBACK_MODE_NO_SPATIAL_LOW_FREQUENCY_EFFECT', 'PLAYBACK_MODE_PAUSED', 'PLAYBACK_MODE_SIMULATE_POSITION', 'PLAYBACK_MODE_SPATIAL', 'PLAYBACK_MODE_SPATIAL_MIX_LEVEL_MATRIX', 'PLAYBACK_MODE_STOP_ON_SIMULATION', 'PLAYBACK_MODE_USE_DOPPLER', 'PLAYBACK_MODE_USE_FILTERS', 'PLAYBACK_MODE_USE_REVERB', 'PeakVolumes', 'PlaybackContextDesc', 'ReadOnly', 'RolloffDesc', 'RolloffType', 'SAVE_FLAG_DEFAULT', 'SAVE_FLAG_STRIP_EVENT_POINTS', 'SAVE_FLAG_STRIP_METADATA', 'SAVE_FLAG_STRIP_PEAKS', 'SPEAKER_FLAG_BACK_CENTER', 'SPEAKER_FLAG_BACK_LEFT', 'SPEAKER_FLAG_BACK_RIGHT', 'SPEAKER_FLAG_FRONT_CENTER', 'SPEAKER_FLAG_FRONT_LEFT', 'SPEAKER_FLAG_FRONT_LEFT_WIDE', 'SPEAKER_FLAG_FRONT_RIGHT', 'SPEAKER_FLAG_FRONT_RIGHT_WIDE', 'SPEAKER_FLAG_LOW_FREQUENCY_EFFECT', 'SPEAKER_FLAG_SIDE_LEFT', 'SPEAKER_FLAG_SIDE_RIGHT', 'SPEAKER_FLAG_TOP_BACK_LEFT', 'SPEAKER_FLAG_TOP_BACK_RIGHT', 'SPEAKER_FLAG_TOP_FRONT_LEFT', 'SPEAKER_FLAG_TOP_FRONT_RIGHT', 'SPEAKER_FLAG_TOP_LEFT', 'SPEAKER_FLAG_TOP_RIGHT', 'SPEAKER_MODE_COUNT', 'SPEAKER_MODE_DEFAULT', 'SPEAKER_MODE_FIVE_POINT_ONE', 'SPEAKER_MODE_FIVE_POINT_ZERO', 'SPEAKER_MODE_FOUR_POINT_ONE', 'SPEAKER_MODE_MONO', 'SPEAKER_MODE_NINE_POINT_ONE', 'SPEAKER_MODE_NINE_POINT_ONE_POINT_FOUR', 'SPEAKER_MODE_NINE_POINT_ONE_POINT_SIX', 'SPEAKER_MODE_QUAD', 'SPEAKER_MODE_SEVEN_POINT_ONE', 'SPEAKER_MODE_SEVEN_POINT_ONE_POINT_FOUR', 'SPEAKER_MODE_SIX_POINT_ONE', 'SPEAKER_MODE_STEREO', 'SPEAKER_MODE_THREE_POINT_ZERO', 'SPEAKER_MODE_TWO_POINT_ONE', 'SPEAKER_MODE_VALID_BITS', 'SampleFormat', 'SoundData', 'SoundDataError', 'SoundFormat', 'Speaker', 'UnitType', 'VOICE_PARAM_ALL', 'VOICE_PARAM_BALANCE', 'VOICE_PARAM_DOPPLER_SCALE', 'VOICE_PARAM_EMITTER', 'VOICE_PARAM_FREQUENCY_RATIO', 'VOICE_PARAM_MATRIX', 'VOICE_PARAM_MUTE', 'VOICE_PARAM_OCCLUSION_FACTOR', 'VOICE_PARAM_PAUSE', 'VOICE_PARAM_PLAYBACK_MODE', 'VOICE_PARAM_PRIORITY', 'VOICE_PARAM_SPATIAL_MIX_LEVEL', 'VOICE_PARAM_VOLUME', 'Voice', 'VoiceParamBalance', 'VoiceParamOcclusion', 'VoiceParams', 'acquire_data_interface', 'acquire_playback_interface', 'get_force_off_playback_mode_flags', 'get_force_on_playback_mode_flags']
AUDIO_IMAGE_FLAG_ALPHA_BLEND: int = 16
AUDIO_IMAGE_FLAG_MULTI_CHANNEL: int = 8
AUDIO_IMAGE_FLAG_NOISE_COLOR: int = 4
AUDIO_IMAGE_FLAG_SPLIT_CHANNELS: int = 32
AUDIO_IMAGE_FLAG_USE_LINES: int = 2
CONE_ANGLE_OMNI_DIRECTIONAL: float = 6.2831854820251465
CONTEXT_FLAG_BAKING: int = 2
CONTEXT_FLAG_MANUAL_STOP: int = 4
CONTEXT_PARAM_ALL: int = 4294967295
CONTEXT_PARAM_DEFAULT_PLAYBACK_MODE: int = 2048
CONTEXT_PARAM_DOPPLER_LIMIT: int = 1024
CONTEXT_PARAM_DOPPLER_SCALE: int = 8
CONTEXT_PARAM_LISTENER: int = 4
CONTEXT_PARAM_MASTER_VOLUME: int = 128
CONTEXT_PARAM_NON_SPATIAL_FREQUENCY_RATIO: int = 64
CONTEXT_PARAM_NON_SPATIAL_VOLUME: int = 512
CONTEXT_PARAM_SPATIAL_FREQUENCY_RATIO: int = 32
CONTEXT_PARAM_SPATIAL_VOLUME: int = 256
CONTEXT_PARAM_SPEED_OF_SOUND: int = 1
CONTEXT_PARAM_VIDEO_LATENCY: int = 8192
CONTEXT_PARAM_VIRTUALIZATION_THRESHOLD: int = 16
CONTEXT_PARAM_WORLD_UNIT_SCALE: int = 2
DATA_FLAG_CALC_PEAKS: int = 16777216
DATA_FLAG_SKIP_EVENT_POINTS: int = 4194304
DATA_FLAG_SKIP_METADATA: int = 2097152
DEFAULT_CHANNEL_COUNT: int = 1
DEFAULT_FORMAT: int = 4
DEFAULT_FRAME_RATE: int = 48000
DEFAULT_SPEED_OF_SOUND: float = 340.0
DEVICE_FLAG_CONNECTED: int = 1
DEVICE_FLAG_DEFAULT: int = 2
DEVICE_FLAG_NOT_OPEN: int = 0
DEVICE_FLAG_STREAMER: int = 4
ENTITY_FLAG_ALL: int = 63
ENTITY_FLAG_CONE: int = 16
ENTITY_FLAG_FORWARD: int = 4
ENTITY_FLAG_MAKE_PERP: int = 2147483648
ENTITY_FLAG_NORMALIZE: int = 1073741824
ENTITY_FLAG_POSITION: int = 1
ENTITY_FLAG_ROLLOFF: int = 32
ENTITY_FLAG_UP: int = 8
ENTITY_FLAG_VELOCITY: int = 2
EVENT_POINT_INVALID_FRAME: int = 18446744073709551615
EVENT_POINT_LOOP_INFINITE: int = 18446744073709551615
INSTANCES_UNLIMITED: int = 0
INVALID_DEVICE_INDEX: int = 18446744073709551615
INVALID_SPEAKER_NAME: int = 18446744073709551615
MAX_CHANNELS: int = 64
MAX_FRAMERATE: int = 200000
MAX_NAME_LENGTH: int = 512
MEMORY_LIMIT_THRESHOLD: int = 2147483648
META_DATA_TAG_ALBUM: str = 'OriginalTitle'
META_DATA_TAG_ARCHIVAL_LOCATION: str = 'Archival Location'
META_DATA_TAG_ARTIST: str = 'Artist'
META_DATA_TAG_AUDIO_SOURCE_WEBSITE: str = 'AudioSourceWebsite'
META_DATA_TAG_BPM: str = 'BPM'
META_DATA_TAG_CLEAR_ALL_TAGS = None
META_DATA_TAG_COMMENT: str = 'Comment'
META_DATA_TAG_COMMISSIONED: str = 'Commissioned'
META_DATA_TAG_COMPOSER: str = 'Composer'
META_DATA_TAG_CONTACT: str = 'Contact'
META_DATA_TAG_COPYRIGHT: str = 'Copyright'
META_DATA_TAG_CREATION_DATE: str = 'Date'
META_DATA_TAG_CROPPED: str = 'Cropped'
META_DATA_TAG_DESCRIPTION: str = 'Description'
META_DATA_TAG_DIMENSIONS: str = 'Dimensions'
META_DATA_TAG_DISC: str = 'Disc'
META_DATA_TAG_DPI: str = 'Dots Per Inch'
META_DATA_TAG_EDITOR: str = 'Editor'
META_DATA_TAG_ENCODER: str = 'Encoder'
META_DATA_TAG_END_TIME: str = 'EndTime'
META_DATA_TAG_ENGINEER: str = 'Engineer'
META_DATA_TAG_FILE_NAME: str = 'FileName'
META_DATA_TAG_GENRE: str = 'Genre'
META_DATA_TAG_INITIAL_KEY: str = 'InitialKey'
META_DATA_TAG_INTERNET_ARTIST_WEBSITE: str = 'ArtistWebsite'
META_DATA_TAG_INTERNET_COMMERCIAL_INFORMATION_URL: str = 'CommercialInformationUrl'
META_DATA_TAG_INTERNET_COPYRIGHT_URL: str = 'CopyrightUrl'
META_DATA_TAG_INTERNET_RADIO_STATION_NAME: str = 'InternetRadioStationName'
META_DATA_TAG_INTERNET_RADIO_STATION_OWNER: str = 'InternetRadioStationOwner'
META_DATA_TAG_INTERNET_RADIO_STATION_URL: str = 'InternetRadioStationUrl'
META_DATA_TAG_ISRC: str = 'ISRC'
META_DATA_TAG_KEYWORDS: str = 'Keywords'
META_DATA_TAG_LANGUAGE: str = 'Language'
META_DATA_TAG_LICENSE: str = 'License'
META_DATA_TAG_LIGHTNESS: str = 'Lightness'
META_DATA_TAG_LOCATION: str = 'Location'
META_DATA_TAG_MEDIUM: str = 'Medium'
META_DATA_TAG_ORGANIZATION: str = 'Organization'
META_DATA_TAG_ORIGINAL_YEAR: str = 'OriginalYear'
META_DATA_TAG_OWNER: str = 'Owner'
META_DATA_TAG_PALETTE_SETTING: str = 'Palette Setting'
META_DATA_TAG_PAYMENT_URL: str = 'PaymentUrl'
META_DATA_TAG_PERFORMER: str = 'OriginalPerformer'
META_DATA_TAG_PLAYLIST_DELAY: str = 'PlaylistDelay'
META_DATA_TAG_PUBLISHER: str = 'Publisher'
META_DATA_TAG_RECORDING_DATE: str = 'RecordingDate'
META_DATA_TAG_SHARPNESS: str = 'Sharpness'
META_DATA_TAG_SOURCE_FORM: str = 'Source Form'
META_DATA_TAG_SPEED: str = 'Speed'
META_DATA_TAG_START_TIME: str = 'StartTime'
META_DATA_TAG_SUBGENRE: str = 'SubGenre'
META_DATA_TAG_SUBJECT: str = 'Subject'
META_DATA_TAG_TECHNICIAN: str = 'Technician'
META_DATA_TAG_TERMS_OF_USE: str = 'TermsOfUse'
META_DATA_TAG_TITLE: str = 'Title'
META_DATA_TAG_TRACK_NUMBER: str = 'TrackNumber'
META_DATA_TAG_VERSION: str = 'Version'
META_DATA_TAG_WEBSITE: str = 'Website'
META_DATA_TAG_WRITER: str = 'OriginalWriter'
MIN_CHANNELS: int = 1
MIN_FRAMERATE: int = 1000
OUTPUT_FLAG_DEVICE: int = 1
PLAYBACK_MODE_DEFAULT_DISTANCE_DELAY: int = 536870912
PLAYBACK_MODE_DEFAULT_INTERAURAL_DELAY: int = 268435456
PLAYBACK_MODE_DEFAULT_USE_DOPPLER: int = 1073741824
PLAYBACK_MODE_DEFAULT_USE_FILTERS: int = 67108864
PLAYBACK_MODE_DEFAULT_USE_REVERB: int = 134217728
PLAYBACK_MODE_DISTANCE_DELAY: int = 4
PLAYBACK_MODE_FADE_IN: int = 512
PLAYBACK_MODE_INTERAURAL_DELAY: int = 8
PLAYBACK_MODE_LISTENER_RELATIVE: int = 2
PLAYBACK_MODE_MUTED: int = 128
PLAYBACK_MODE_NO_POSITION_SIMULATION: int = 2048
PLAYBACK_MODE_NO_SPATIAL_LOW_FREQUENCY_EFFECT: int = 8192
PLAYBACK_MODE_PAUSED: int = 256
PLAYBACK_MODE_SIMULATE_POSITION: int = 1024
PLAYBACK_MODE_SPATIAL: int = 1
PLAYBACK_MODE_SPATIAL_MIX_LEVEL_MATRIX: int = 4096
PLAYBACK_MODE_STOP_ON_SIMULATION: int = 16384
PLAYBACK_MODE_USE_DOPPLER: int = 16
PLAYBACK_MODE_USE_FILTERS: int = 64
PLAYBACK_MODE_USE_REVERB: int = 32
SAVE_FLAG_DEFAULT: int = 0
SAVE_FLAG_STRIP_EVENT_POINTS: int = 2
SAVE_FLAG_STRIP_METADATA: int = 1
SAVE_FLAG_STRIP_PEAKS: int = 4
SPEAKER_FLAG_BACK_CENTER: int = 16
SPEAKER_FLAG_BACK_LEFT: int = 128
SPEAKER_FLAG_BACK_RIGHT: int = 256
SPEAKER_FLAG_FRONT_CENTER: int = 4
SPEAKER_FLAG_FRONT_LEFT: int = 1
SPEAKER_FLAG_FRONT_LEFT_WIDE: int = 8192
SPEAKER_FLAG_FRONT_RIGHT: int = 2
SPEAKER_FLAG_FRONT_RIGHT_WIDE: int = 16384
SPEAKER_FLAG_LOW_FREQUENCY_EFFECT: int = 8
SPEAKER_FLAG_SIDE_LEFT: int = 32
SPEAKER_FLAG_SIDE_RIGHT: int = 64
SPEAKER_FLAG_TOP_BACK_LEFT: int = 2048
SPEAKER_FLAG_TOP_BACK_RIGHT: int = 4096
SPEAKER_FLAG_TOP_FRONT_LEFT: int = 512
SPEAKER_FLAG_TOP_FRONT_RIGHT: int = 1024
SPEAKER_FLAG_TOP_LEFT: int = 32768
SPEAKER_FLAG_TOP_RIGHT: int = 65536
SPEAKER_MODE_COUNT: int = 7
SPEAKER_MODE_DEFAULT: int = 0
SPEAKER_MODE_FIVE_POINT_ONE: int = 63
SPEAKER_MODE_FIVE_POINT_ZERO: int = 55
SPEAKER_MODE_FOUR_POINT_ONE: int = 59
SPEAKER_MODE_MONO: int = 1
SPEAKER_MODE_NINE_POINT_ONE: int = 25023
SPEAKER_MODE_NINE_POINT_ONE_POINT_FOUR: int = 32703
SPEAKER_MODE_NINE_POINT_ONE_POINT_SIX: int = 131007
SPEAKER_MODE_QUAD: int = 51
SPEAKER_MODE_SEVEN_POINT_ONE: int = 447
SPEAKER_MODE_SEVEN_POINT_ONE_POINT_FOUR: int = 8127
SPEAKER_MODE_SIX_POINT_ONE: int = 463
SPEAKER_MODE_STEREO: int = 3
SPEAKER_MODE_THREE_POINT_ZERO: int = 7
SPEAKER_MODE_TWO_POINT_ONE: int = 11
SPEAKER_MODE_VALID_BITS: int = 131007
VOICE_PARAM_ALL: int = 4294967295
VOICE_PARAM_BALANCE: int = 8
VOICE_PARAM_DOPPLER_SCALE: int = 256
VOICE_PARAM_EMITTER: int = 1024
VOICE_PARAM_FREQUENCY_RATIO: int = 16
VOICE_PARAM_MATRIX: int = 2048
VOICE_PARAM_MUTE: int = 4
VOICE_PARAM_OCCLUSION_FACTOR: int = 512
VOICE_PARAM_PAUSE: int = 64
VOICE_PARAM_PLAYBACK_MODE: int = 1
VOICE_PARAM_PRIORITY: int = 32
VOICE_PARAM_SPATIAL_MIX_LEVEL: int = 128
VOICE_PARAM_VOLUME: int = 2
