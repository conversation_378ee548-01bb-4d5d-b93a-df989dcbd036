"""
pybind11 carb.dictionary bindings
"""
from __future__ import annotations
import typing
__all__ = ['IDictionary', 'ISerializer', 'Item', 'ItemType', 'UpdateAction', 'acquire_dictionary_interface', 'acquire_serializer_interface', 'get_json_serializer', 'get_toml_serializer']
class IDictionary:
    def create_item(self, arg0: typing.Any, arg1: str, arg2: ItemType) -> Item:
        ...
    def destroy_item(self, arg0: Item) -> None:
        ...
    def get(self, base_item: Item, path: str = '') -> typing.Any:
        ...
    def get_array_length(self, arg0: Item) -> int:
        ...
    def get_as_bool(self, arg0: Item) -> bool:
        ...
    def get_as_float(self, arg0: Item) -> float:
        ...
    def get_as_int(self, arg0: Item) -> int:
        ...
    def get_as_string(self, base_item: Item, path: str = '') -> str:
        ...
    def get_dict_copy(self, base_item: Item, path: str = '') -> typing.Any:
        """
                    Creates python object from the supplied dictionary at path (supplied item is unchanged). Item is calculated
                    via the path relative to the base item.
        
                    Args:
                        base_item: The base item.
                        path: Path, relative to the base item - to the item
        
                    Returns:
                        Python object with copies of the item data.
        """
    def get_item(self, base_item: Item, path: str = '') -> Item:
        ...
    def get_item_child_by_index(self, arg0: Item, arg1: int) -> Item:
        ...
    def get_item_child_by_index_mutable(self, arg0: Item, arg1: int) -> Item:
        ...
    def get_item_child_count(self, arg0: Item) -> int:
        ...
    def get_item_mutable(self, base_item: Item, path: str = '') -> Item:
        ...
    def get_item_name(self, base_item: Item, path: str = '') -> str:
        ...
    def get_item_parent(self, arg0: Item) -> Item:
        ...
    def get_item_parent_mutable(self, arg0: Item) -> Item:
        ...
    def get_item_type(self, arg0: Item) -> ItemType:
        ...
    def get_preferred_array_type(self, arg0: Item) -> ItemType:
        ...
    def is_accessible_as(self, arg0: ItemType, arg1: Item) -> bool:
        ...
    def is_accessible_as_array_of(self, arg0: ItemType, arg1: Item) -> bool:
        ...
    def readLock(self, arg0: Item) -> None:
        ...
    def set(self, item: Item, path: str = '', value: typing.Any) -> None:
        ...
    def set_bool(self, arg0: Item, arg1: bool) -> None:
        ...
    def set_bool_array(self, arg0: Item, arg1: list[bool]) -> None:
        ...
    def set_float(self, arg0: Item, arg1: float) -> None:
        ...
    def set_float_array(self, arg0: Item, arg1: list[float]) -> None:
        ...
    def set_int(self, arg0: Item, arg1: int) -> None:
        ...
    def set_int_array(self, arg0: Item, arg1: list[int]) -> None:
        ...
    def set_string(self, arg0: Item, arg1: str) -> None:
        ...
    def set_string_array(self, arg0: Item, arg1: list[str]) -> None:
        ...
    def unlock(self, arg0: Item) -> None:
        ...
    def update(self, arg0: Item, arg1: str, arg2: Item, arg3: str, arg4: typing.Any) -> None:
        ...
    def writeLock(self, arg0: Item) -> None:
        ...
class ISerializer:
    def create_dictionary_from_file(self, path: str) -> Item:
        ...
    def create_dictionary_from_string_buffer(self, arg0: str) -> Item:
        ...
    def create_string_buffer_from_dictionary(self, item: Item, ser_options: int = 0) -> str:
        ...
    def save_file_from_dictionary(self, dict: Item, path: str, options: int = 0) -> None:
        ...
class Item:
    def __bool__(self) -> bool:
        ...
    def __contains__(self, arg0: typing.Any) -> bool:
        ...
    def __getitem__(self, arg0: str) -> typing.Any:
        ...
    def __len__(self) -> int:
        ...
    def __repr__(self) -> str:
        ...
    def __setitem__(self, arg0: str, arg1: typing.Any) -> None:
        ...
    def __str__(self) -> str:
        ...
    def clear(self) -> None:
        ...
    def get(self, arg0: str, arg1: typing.Any) -> typing.Any:
        ...
    def get_dict(self) -> typing.Any:
        ...
    def get_key_at(self, arg0: int) -> typing.Any:
        ...
    def get_keys(self) -> list[str]:
        ...
class ItemType:
    """
    Members:
    
      BOOL
    
      INT
    
      FLOAT
    
      STRING
    
      DICTIONARY
    
      COUNT
    """
    BOOL: typing.ClassVar[ItemType]  # value = <ItemType.BOOL: 0>
    COUNT: typing.ClassVar[ItemType]  # value = <ItemType.COUNT: 5>
    DICTIONARY: typing.ClassVar[ItemType]  # value = <ItemType.DICTIONARY: 4>
    FLOAT: typing.ClassVar[ItemType]  # value = <ItemType.FLOAT: 2>
    INT: typing.ClassVar[ItemType]  # value = <ItemType.INT: 1>
    STRING: typing.ClassVar[ItemType]  # value = <ItemType.STRING: 3>
    __members__: typing.ClassVar[dict[str, ItemType]]  # value = {'BOOL': <ItemType.BOOL: 0>, 'INT': <ItemType.INT: 1>, 'FLOAT': <ItemType.FLOAT: 2>, 'STRING': <ItemType.STRING: 3>, 'DICTIONARY': <ItemType.DICTIONARY: 4>, 'COUNT': <ItemType.COUNT: 5>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class UpdateAction:
    """
    Members:
    
      OVERWRITE
    
      KEEP
    """
    KEEP: typing.ClassVar[UpdateAction]  # value = <UpdateAction.KEEP: 1>
    OVERWRITE: typing.ClassVar[UpdateAction]  # value = <UpdateAction.OVERWRITE: 0>
    __members__: typing.ClassVar[dict[str, UpdateAction]]  # value = {'OVERWRITE': <UpdateAction.OVERWRITE: 0>, 'KEEP': <UpdateAction.KEEP: 1>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
def acquire_dictionary_interface(plugin_name: str = None, library_path: str = None) -> IDictionary:
    ...
def acquire_serializer_interface(plugin_name: str = None, library_path: str = None) -> ISerializer:
    ...
def get_json_serializer() -> ISerializer:
    ...
def get_toml_serializer() -> ISerializer:
    ...
