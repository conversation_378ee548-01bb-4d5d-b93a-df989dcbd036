"""
pybind11 carb.windowing bindings
"""
from __future__ import annotations
import carb._carb
import carb.input
import typing
__all__ = ['Cursor', 'CursorMode', 'CursorStandardShape', 'G<PERSON>ontext', 'IG<PERSON>ontext', 'IWindowing', 'Image', 'InputMode', 'Monitor', 'WINDOW_HINT_FLOATING', 'WINDOW_HINT_MAXIMIZED', 'WINDOW_HINT_NONE', 'WINDOW_HINT_NO_AUTO_ICONIFY', 'WINDOW_HINT_NO_DECORATION', 'WINDOW_HINT_NO_FOCUS_ON_SHOW', 'WINDOW_HINT_NO_RESIZE', 'WINDOW_HINT_SCALE_TO_MONITOR', 'Window', 'acquire_gl_context_interface', 'acquire_windowing_interface']
class Cursor:
    pass
class CursorMode:
    """
    Members:
    
      NORMAL
    
      HIDDEN
    
      DISABLED
    """
    DISABLED: typing.ClassVar[CursorMode]  # value = <CursorMode.DISABLED: 2>
    HIDDEN: typing.ClassVar[CursorMode]  # value = <CursorMode.HIDDEN: 1>
    NORMAL: typing.ClassVar[CursorMode]  # value = <CursorMode.NORMAL: 0>
    __members__: typing.ClassVar[dict[str, CursorMode]]  # value = {'NORMAL': <CursorMode.NORMAL: 0>, 'HIDDEN': <CursorMode.HIDDEN: 1>, 'DISABLED': <CursorMode.DISABLED: 2>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class CursorStandardShape:
    """
    Members:
    
      ARROW
    
      IBEAM
    
      CROSSHAIR
    
      HAND
    
      HORIZONTAL_RESIZE
    
      VERTICAL_RESIZE
    """
    ARROW: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.ARROW: 0>
    CROSSHAIR: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.CROSSHAIR: 2>
    HAND: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.HAND: 3>
    HORIZONTAL_RESIZE: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.HORIZONTAL_RESIZE: 4>
    IBEAM: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.IBEAM: 1>
    VERTICAL_RESIZE: typing.ClassVar[CursorStandardShape]  # value = <CursorStandardShape.VERTICAL_RESIZE: 5>
    __members__: typing.ClassVar[dict[str, CursorStandardShape]]  # value = {'ARROW': <CursorStandardShape.ARROW: 0>, 'IBEAM': <CursorStandardShape.IBEAM: 1>, 'CROSSHAIR': <CursorStandardShape.CROSSHAIR: 2>, 'HAND': <CursorStandardShape.HAND: 3>, 'HORIZONTAL_RESIZE': <CursorStandardShape.HORIZONTAL_RESIZE: 4>, 'VERTICAL_RESIZE': <CursorStandardShape.VERTICAL_RESIZE: 5>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class GLContext:
    pass
class IGLContext:
    def create_context_opengl(self, width: int, height: int) -> GLContext:
        ...
    def create_context_opengles(self, width: int, height: int) -> GLContext:
        ...
    def destroy_context(self, arg0: GLContext) -> None:
        ...
    def make_context_current(self, arg0: GLContext) -> None:
        ...
class IWindowing:
    def create_cursor(self, arg0: Image, arg1: int, arg2: int) -> Cursor:
        ...
    def create_cursor_standard(self, arg0: CursorStandardShape) -> Cursor:
        ...
    def create_window(self, width: int, height: int, title: str, fullscreen: bool, hints: int = 0) -> Window:
        ...
    def destroy_cursor(self, arg0: Cursor) -> None:
        ...
    def destroy_window(self, arg0: Window) -> None:
        ...
    def focus_window(self, arg0: Window) -> None:
        ...
    def get_clipboard(self, arg0: Window) -> str:
        ...
    def get_cursor_mode(self, arg0: Window) -> CursorMode:
        ...
    def get_cursor_position(self, arg0: Window) -> carb._carb.Int2:
        ...
    def get_input_mode(self, arg0: Window, arg1: InputMode) -> bool:
        ...
    def get_keyboard(self, arg0: Window) -> carb.input.Keyboard:
        ...
    def get_monitor_position(self, arg0: Monitor) -> carb._carb.Int2:
        ...
    def get_monitor_work_area(self, arg0: Monitor) -> tuple:
        ...
    def get_monitors(self) -> tuple:
        ...
    def get_mouse(self, arg0: Window) -> carb.input.Mouse:
        ...
    def get_native_display(self, arg0: Window) -> capsule:
        ...
    def get_native_window(self, arg0: Window) -> capsule:
        ...
    def get_window_height(self, arg0: Window) -> int:
        ...
    def get_window_opacity(self, arg0: Window) -> float:
        ...
    def get_window_position(self, arg0: Window) -> carb._carb.Int2:
        ...
    def get_window_user_pointer(self, arg0: Window) -> capsule:
        ...
    def get_window_width(self, arg0: Window) -> int:
        ...
    def hide_window(self, arg0: Window) -> None:
        ...
    def is_window_focused(self, arg0: Window) -> bool:
        ...
    def is_window_fullscreen(self, arg0: Window) -> bool:
        ...
    def is_window_maximized(self, arg0: Window) -> bool:
        ...
    def is_window_minimized(self, arg0: Window) -> bool:
        ...
    def maximize_window(self, arg0: Window) -> None:
        ...
    def minimize_window(self, arg0: Window) -> None:
        ...
    def poll_events(self) -> None:
        ...
    def resize_window(self, arg0: Window, arg1: int, arg2: int) -> None:
        ...
    def restore_window(self, arg0: Window) -> None:
        ...
    def set_clipboard(self, arg0: Window, arg1: str) -> None:
        ...
    def set_cursor(self, arg0: Window, arg1: Cursor) -> None:
        ...
    def set_cursor_mode(self, arg0: Window, arg1: CursorMode) -> None:
        ...
    def set_cursor_position(self, arg0: Window, arg1: carb._carb.Int2) -> None:
        ...
    def set_input_mode(self, arg0: Window, arg1: InputMode, arg2: bool) -> None:
        ...
    def set_window_content_scale(self, arg0: Window) -> carb._carb.Float2:
        ...
    def set_window_fullscreen(self, arg0: Window, arg1: bool) -> None:
        ...
    def set_window_icon(self, arg0: Window, arg1: Image) -> None:
        ...
    def set_window_opacity(self, arg0: Window, arg1: float) -> None:
        ...
    def set_window_position(self, arg0: Window, arg1: carb._carb.Int2) -> None:
        ...
    def set_window_should_close(self, arg0: Window, arg1: bool) -> None:
        ...
    def set_window_title(self, arg0: Window, arg1: str) -> None:
        ...
    def set_window_user_pointer(self, arg0: Window, arg1: capsule) -> None:
        ...
    def should_window_close(self, arg0: Window) -> bool:
        ...
    def show_window(self, arg0: Window) -> None:
        ...
    def translate_key(self, arg0: carb.input.KeyboardInput) -> carb.input.KeyboardInput:
        ...
    def update_input_devices(self) -> None:
        ...
    def wait_events(self) -> None:
        ...
class Image:
    def __init__(self, width: int, height: int, pixels: bytes) -> None:
        ...
class InputMode:
    """
    Members:
    
      STICKY_KEYS
    
      STICKY_MOUSE_BUTTONS
    
      LOCK_KEY_MODS
    
      RAW_MOUSE_MOTION
    """
    LOCK_KEY_MODS: typing.ClassVar[InputMode]  # value = <InputMode.LOCK_KEY_MODS: 2>
    RAW_MOUSE_MOTION: typing.ClassVar[InputMode]  # value = <InputMode.RAW_MOUSE_MOTION: 3>
    STICKY_KEYS: typing.ClassVar[InputMode]  # value = <InputMode.STICKY_KEYS: 0>
    STICKY_MOUSE_BUTTONS: typing.ClassVar[InputMode]  # value = <InputMode.STICKY_MOUSE_BUTTONS: 1>
    __members__: typing.ClassVar[dict[str, InputMode]]  # value = {'STICKY_KEYS': <InputMode.STICKY_KEYS: 0>, 'STICKY_MOUSE_BUTTONS': <InputMode.STICKY_MOUSE_BUTTONS: 1>, 'LOCK_KEY_MODS': <InputMode.LOCK_KEY_MODS: 2>, 'RAW_MOUSE_MOTION': <InputMode.RAW_MOUSE_MOTION: 3>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class Monitor:
    pass
class Window:
    pass
def acquire_gl_context_interface(plugin_name: str = None, library_path: str = None) -> IGLContext:
    ...
def acquire_windowing_interface(plugin_name: str = None, library_path: str = None) -> IWindowing:
    ...
WINDOW_HINT_FLOATING: int = 32
WINDOW_HINT_MAXIMIZED: int = 64
WINDOW_HINT_NONE: int = 0
WINDOW_HINT_NO_AUTO_ICONIFY: int = 4
WINDOW_HINT_NO_DECORATION: int = 2
WINDOW_HINT_NO_FOCUS_ON_SHOW: int = 8
WINDOW_HINT_NO_RESIZE: int = 1
WINDOW_HINT_SCALE_TO_MONITOR: int = 16
