from __future__ import annotations
import typing
__all__ = ['ILogging', 'LEVEL_ERROR', 'LEVEL_FATAL', 'LEVEL_INFO', 'LEVEL_VERBOSE', 'LEVEL_WARN', 'LogSettingBehavior', 'Logger', 'LoggerHandle', 'acquire_logging']
class ILogging:
    def add_logger(self, arg0: typing.Callable[[str, int, str, int, str], None]) -> LoggerHandle:
        ...
    def get_level_threshold(self) -> int:
        ...
    def is_log_enabled(self) -> bool:
        ...
    def remove_logger(self, arg0: LoggerHandle) -> None:
        ...
    def reset(self) -> None:
        ...
    def set_level_threshold(self, arg0: int) -> None:
        ...
    def set_level_threshold_for_source(self, arg0: str, arg1: LogSettingBehavior, arg2: int) -> None:
        ...
    def set_log_enabled(self, arg0: bool) -> None:
        ...
    def set_log_enabled_for_source(self, arg0: str, arg1: LogSettingBehavior, arg2: bool) -> None:
        ...
class LogSettingBehavior:
    """
    Members:
    
      INHERIT
    
      OVERRIDE
    """
    INHERIT: typing.ClassVar[LogSettingBehavior]  # value = <LogSettingBehavior.INHERIT: 0>
    OVERRIDE: typing.ClassVar[LogSettingBehavior]  # value = <LogSettingBehavior.OVERRIDE: 1>
    __members__: typing.ClassVar[dict[str, LogSettingBehavior]]  # value = {'INHERIT': <LogSettingBehavior.INHERIT: 0>, 'OVERRIDE': <LogSettingBehavior.OVERRIDE: 1>}
    def __eq__(self, other: typing.Any) -> bool:
        ...
    def __getstate__(self) -> int:
        ...
    def __hash__(self) -> int:
        ...
    def __index__(self) -> int:
        ...
    def __init__(self, value: int) -> None:
        ...
    def __int__(self) -> int:
        ...
    def __ne__(self, other: typing.Any) -> bool:
        ...
    def __repr__(self) -> str:
        ...
    def __setstate__(self, state: int) -> None:
        ...
    def __str__(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def value(self) -> int:
        ...
class Logger:
    pass
class LoggerHandle(Logger):
    pass
def acquire_logging(plugin_name: str = None, library_path: str = None) -> ILogging:
    ...
LEVEL_ERROR: int = 1
LEVEL_FATAL: int = 2
LEVEL_INFO: int = -1
LEVEL_VERBOSE: int = -2
LEVEL_WARN: int = 0
