from __future__ import annotations
import pxr.Usd
from pxr import Usd
__all__ = ['Usd', 'add_cube', 'set_physics_frequency', 'set_scene_physics_type', 'simulate']
def add_cube(stage, path, size, offset, physics = True, mass = 0.0) -> pxr.Usd.Prim:
    ...
def set_physics_frequency(frequency = 60):
    ...
def set_scene_physics_type(gpu = False, scene_path = '/physicsScene'):
    ...
def simulate(seconds, dc = None, art = None, steps_per_sec = 60):
    ...
