from __future__ import annotations
import abc
from abc import ABC
from abc import abstractmethod
import argparse as argparse
import carb as carb
import collections
from collections import defaultdict
import inspect as inspect
from omni.kit.commands.on_change import _dispatch_changed
import re as re
import sys as sys
import typing
__all__ = ['ABC', 'CallbackID', 'Command', 'CommandParameter', 'LOGGING_ENABLED', 'POST_DO_CALLBACK', 'POST_UNDO_CALLBACK', 'PRE_DO_CALLBACK', 'PRE_UNDO_CALLBACK', 'abstractmethod', 'argparse', 'carb', 'create', 'defaultdict', 'execute', 'execute_argv', 'get_argument_parser_from_function', 'get_command_class', 'get_command_class_signature', 'get_command_doc', 'get_command_parameters', 'get_commands', 'get_commands_list', 'inspect', 're', 'register', 'register_all_commands_in_module', 'register_callback', 'set_logging_enabled', 'sys', 'unregister', 'unregister_callback', 'unregister_module_commands']
class CallbackID:
    def __init__(self, command_name, module_name, callback_type, callback):
        ...
    def get(self):
        ...
class Command(abc.ABC):
    """
    Base class for all **Commands**.
    """
    __abstractmethods__: typing.ClassVar[frozenset]  # value = frozenset({'do'})
    _abc_impl: typing.ClassVar[_abc._abc_data]  # value = <_abc._abc_data object>
    def do(self):
        ...
    def modify_callback_info(self, cb_type: str, args: typing.Dict[str, typing.Any]) -> typing.Dict[str, typing.Any]:
        """
        Returns a dictionary of information to be passed to callbacks of the given type.
        
                By default callbacks are passed a copy of the arguments which were passed to **execute()** when the command
                was invoked. This method can be overridden to modify that information for specific callback types.
        
                Args:
                    cb_type: Type of callback the information will be passed to.
                    args: A dictionary containing a copy of the arguments with which the command was invoked. This is a
                          shallow copy so implementations may add, remove or replace dictionary elements but should not
                          modify any of the objects contained within it.
        
                Returns:
                    A dictionary of information to be passed to callbacks of the specified type.
                
        """
class CommandParameter:
    """
    Parameter details from inspect.Parameter with documentation if present
    """
    def __init__(self, param: inspect.Parameter, doc):
        ...
    @property
    def default(self):
        ...
    @property
    def default_str(self) -> str:
        ...
    @property
    def doc(self) -> str:
        ...
    @property
    def name(self) -> str:
        ...
    @property
    def type(self):
        ...
    @property
    def type_str(self) -> str:
        ...
def _call_callbacks(command: Command, name: str, kwargs: typing.Dict[str, typing.Any], cb_type: str):
    ...
def _get_argument_doc(doc):
    ...
def _get_callback_info(cb_type: str, command: Command, cmd_args: typing.Dict[str, typing.Any]) -> typing.Dict[str, typing.Any]:
    ...
def _get_module_and_class(name: str) -> typing.Tuple[str, str]:
    ...
def _get_module_and_stripped_class(name: str) -> typing.Tuple[str, str]:
    ...
def _log_error(message: str):
    ...
def create(name, **kwargs):
    """
    Create **Command** object.
    
        Args:
            name: **Command** name.
            **kwargs: Arbitrary keyword arguments to be passed into into **Command** constructor.
    
        Returns:
            **Command** object if succeeded. `None` if failed.
        
    """
def execute(name, **kwargs) -> typing.Tuple[bool, typing.Any]:
    """
    Execute **Command**.
    
        Args:
            name: **Command** name. Can be class name (e.g. "My") or full name including module (e.g. "foo.bar.MyCommand")
            **kwargs: Arbitrary keyword arguments to be passed into into **Command** constructor.
        
    """
def execute_argv(name, argv: list) -> typing.Tuple[bool, typing.Any]:
    """
    Execute **Command** using argument list..
    
        Attempts to convert argument list into **Command**'s kwargs. If a **Command** has *get_argument_parser* method defined, it will be called to get :class:`argparse.ArgumentParser` instance to use for parsing.
        Otherwise command docstring is used to extract argument information.
    
        Args:
            name: **Command** name.
            argv: Argument list.
        
    """
def get_argument_parser_from_function(function):
    ...
def get_command_class(name: str) -> typing.Type[omni.kit.commands.command.Command]:
    """
    Get **Command** class(type) by name.
    
        Args:
            name: **Command** name. It may include a module name to be more specific and avoid conflicts.
    
        Returns:
            **Command** class if succeeded. `None` if can't find a command with this name.
        
    """
def get_command_class_signature(name: str):
    """
    Get the init signature for a **Command**.
    
        Args:
            name: **Command** name. It may include a module name to be more specific and avoid conflicts.
    
        Returns:
            __init__ signature
        
    """
def get_command_doc(name: str) -> str:
    """
    Get **Command** docstring by name.
    
        Args:
            name: **Command** name. It may include a module name to be more specific and avoid conflicts.
    
        Returns:
            Python docstring (__doc__) from a command type.
        
    """
def get_command_parameters(name: str) -> typing.List[typing.Type[omni.kit.commands.command.CommandParameter]]:
    """
    Get all parameters for a **Commands**.
    
        Args:
            name: **Command** name. It may include a module name to be more specific and avoid conflicts.
    
        Returns:
            A list of **CommandParameter** (except 'self' parameter)
        
    """
def get_commands():
    """
    Get all registered **Commands**.
    """
def get_commands_list() -> typing.List[typing.Type[omni.kit.commands.command.Command]]:
    """
    Return list of all **Command** classes registered.
    """
def register(command_class: typing.Type[omni.kit.commands.command.Command]):
    """
    Register a **Command**.
    
        Args:
            command_class: **Command** class.
        
    """
def register_all_commands_in_module(module):
    """
    Register all **Commands** found in specified module.
    
        Register all classes in module which inherit :class:`omni.kit.commands.Command`.
    
        Args:
            module: Module name or module object.
    
        Returns:
            An accessor object that contains a function for every command to execute it. e.g. if you register
            the commands "Run" and "Hide" then the accessor behaves like:
    
            .. code-block:: python
    
                class Accessor:
                    @staticmethod
                    def Run(**kwargs):
                        execute("Run", **kwargs)
                    @staticmethod
                    def Hide(**kwargs):
                        execute("Hide", **kwargs)
    
            This gives you a nicer syntax for running your commands:
    
            .. code-block:: python
    
                cmds = register_all_commands_in_module(module)
                cmds.Run(3.14)
                cmds.Hide("Behind the tree")
        
    """
def register_callback(name: str, cb_type: str, callback: typing.Callable[[typing.Dict[str, typing.Any]], NoneType]) -> CallbackID:
    """
    Register a callback for a command.
    
        Args:
            name: **Command** name. It may include a module name to be more specific and avoid conflicts.
            cb_type: Type of callback. Currently supported types are:
                         PRE_DO_CALLBACK - called before the command is executed
                         POST_DO_CALLBACK - called after the command is executed
                         PRE_UNDO_CALLBACK - called before the command is undone
                         POST_UNDO_CALLBACK - called after the command is undone
            callback: Callable to be called. Will be passed a dictionary of information specific to that
                      command invocation. By default the dictionary will contain the parameters passed to
                      execute(), but this may be overridden by individual commands so check their documentation.
                      Many command parameters are optional so it is important that callbacks check for their
                      presence before attempting to access them. The callback must not make any changes to
                      the dictionary passed to it.
    
        Returns:
            An ID that can be passed to **unregister_callback**.
        
    """
def set_logging_enabled(enabled: bool):
    ...
def unregister(command_class: typing.Type[omni.kit.commands.command.Command]):
    """
    Unregister a **Command**.
    
        Args:
            command_class: **Command** class.
        
    """
def unregister_callback(id: CallbackID):
    """
    Unregister a command callback previously registered through **register_callback**.
    
        Args:
            id: The ID returned by **register_callback** when the callback was registered.
        
    """
def unregister_module_commands(command_interface):
    """
    Unregister the list of commands registered by register_all_commands_in_module
    
        Args:
            command_interface: An object whose only members are command classes that were registered
        
    """
LOGGING_ENABLED: bool = True
POST_DO_CALLBACK: str = 'post_do'
POST_UNDO_CALLBACK: str = 'post_undo'
PRE_DO_CALLBACK: str = 'pre_do'
PRE_UNDO_CALLBACK: str = 'pre_undo'
_callbacks: collections.defaultdict  # value = defaultdict(<function <lambda> at 0x709fbf772950>, {('DeletePrims', ''): defaultdict(<class 'list'>, {'pre_do': [<bound method _PublicExtension.__on_before_del_prim of <omni.graph.core._impl.extension._PublicExtension object>>], 'post_do': [functools.partial(<function PrimCmdCallback._on_delete_prim_cmd_do at 0x709eecf51ab0>, <weakproxy at 0x709eecfd1df0 to PrimCmdCallback at 0x709eecf5c460>)]}), ('URDFCreateImportConfig', ''): defaultdict(<class 'list'>, {'pre_do': [], 'post_do': []}), ('CopyPrim', ''): defaultdict(<class 'list'>, {'post_do': [functools.partial(<function PrimCmdCallback._on_copy_prim_cmd_do at 0x709eecf51b40>, <weakproxy at 0x709eecfd1df0 to PrimCmdCallback at 0x709eecf5c460>), <bound method MetricsAssemblerManager.prim_copy_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>]}), ('MJCFCreateImportConfig', ''): defaultdict(<class 'list'>, {'pre_do': [], 'post_do': []}), ('MovePrim', ''): defaultdict(<class 'list'>, {'pre_do': [functools.partial(<bound method LiveSyncing.__on_pre_move_prim of <omni.kit.usd.layers._impl.live_syncing.LiveSyncing object>>, undo=False), <bound method MetricsAssemblerManager.prim_move_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>], 'post_do': [functools.partial(<bound method LiveSyncing.__on_post_move_prim of <omni.kit.usd.layers._impl.live_syncing.LiveSyncing object>>, undo=False)], 'pre_undo': [functools.partial(<bound method LiveSyncing.__on_pre_move_prim of <omni.kit.usd.layers._impl.live_syncing.LiveSyncing object>>, undo=True)], 'post_undo': [functools.partial(<bound method LiveSyncing.__on_post_move_prim of <omni.kit.usd.layers._impl.live_syncing.LiveSyncing object>>, undo=True)]}), ('CreateReference', ''): defaultdict(<class 'list'>, {'post_do': [<bound method MetricsAssemblerManager.create_post_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>], 'pre_do': [<bound method MetricsAssemblerManager.create_pre_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>]}), ('CreatePayload', ''): defaultdict(<class 'list'>, {'post_do': [<bound method MetricsAssemblerManager.create_post_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>], 'pre_do': [<bound method MetricsAssemblerManager.create_pre_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>]}), ('AddReference', ''): defaultdict(<class 'list'>, {'post_do': [<bound method MetricsAssemblerManager.add_reference_post_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>], 'pre_do': [<bound method MetricsAssemblerManager.add_reference_pre_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>]}), ('AddPayload', ''): defaultdict(<class 'list'>, {'post_do': [<bound method MetricsAssemblerManager.add_payload_post_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>], 'pre_do': [<bound method MetricsAssemblerManager.add_payload_pre_do_callback of <omni.metrics.assembler.ui.metricsAssemblerManager.MetricsAssemblerManager object>>]})})
_commands: collections.defaultdict  # value = defaultdict(<class 'dict'>, {'Redo': {'omni.kit.undo.undo': <class 'omni.kit.undo.undo.Redo'>}, 'Repeat': {'omni.kit.undo.undo': <class 'omni.kit.undo.undo.Repeat'>}, 'Undo': {'omni.kit.undo.undo': <class 'omni.kit.undo.undo.Undo'>}, 'TransformPrimSRTCpp': {'omni.usd': <class 'omni.usd.TransformPrimSRTCpp'>}, 'TransformMultiPrimsSRTCpp': {'omni.usd': <class 'omni.usd.TransformMultiPrimsSRTCpp'>}, 'SerializeAsset': {'omni.usd': <class 'omni.usd.SerializeAsset'>}, 'CreateCompoundNodeTypeCommand': {}, 'CreateCompoundNodeType': {'omni.graph.core._impl.unstable.compound_commands': <class 'omni.graph.core._impl.unstable.compound_commands.CreateCompoundNodeTypeCommand'>}, 'CreateCompoundNodeTypeInput': {'omni.graph.core._impl.unstable.compound_commands': <class 'omni.graph.core._impl.unstable.compound_commands.CreateCompoundNodeTypeInput'>}, 'CreateCompoundNodeTypeOutput': {'omni.graph.core._impl.unstable.compound_commands': <class 'omni.graph.core._impl.unstable.compound_commands.CreateCompoundNodeTypeOutput'>}, 'CreateCompoundSubgraphCommand': {}, 'CreateCompoundSubgraph': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.CreateCompoundSubgraphCommand'>}, 'CreateCompoundSubgraphInputCommand': {}, 'CreateCompoundSubgraphInput': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.CreateCompoundSubgraphInputCommand'>}, 'CreateCompoundSubgraphOutputCommand': {}, 'CreateCompoundSubgraphOutput': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.CreateCompoundSubgraphOutputCommand'>}, 'PromoteUnconnectedToCompoundSubgraphCommand': {}, 'PromoteUnconnectedToCompoundSubgraph': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.PromoteUnconnectedToCompoundSubgraphCommand'>}, 'RemoveCompoundNodeTypeInput': {'omni.graph.core._impl.unstable.compound_commands': <class 'omni.graph.core._impl.unstable.compound_commands.RemoveCompoundNodeTypeInput'>}, 'RemoveCompoundNodeTypeOutput': {'omni.graph.core._impl.unstable.compound_commands': <class 'omni.graph.core._impl.unstable.compound_commands.RemoveCompoundNodeTypeOutput'>}, 'RemoveCompoundSubgraphAttributeCommand': {}, 'RemoveCompoundSubgraphAttribute': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.RemoveCompoundSubgraphAttributeCommand'>}, 'RenameCompoundSubgraphAttributeCommand': {}, 'RenameCompoundSubgraphAttribute': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.RenameCompoundSubgraphAttributeCommand'>}, 'RenameCompoundSubgraphCommand': {}, 'RenameCompoundSubgraph': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.RenameCompoundSubgraphCommand'>}, 'ReplaceWithCompoundSubgraphCommand': {}, 'ReplaceWithCompoundSubgraph': {'omni.graph.core._impl.unstable.subgraph_compound_commands': <class 'omni.graph.core._impl.unstable.subgraph_compound_commands.ReplaceWithCompoundSubgraphCommand'>}, 'ApplyOmniGraphAPICommand': {}, 'ApplyOmniGraphAPI': {'omni.graph.core._impl.commands.instancing_commands': <class 'omni.graph.core._impl.commands.instancing_commands.ApplyOmniGraphAPICommand'>}, 'RemoveOmniGraphAPICommand': {}, 'RemoveOmniGraphAPI': {'omni.graph.core._impl.commands.instancing_commands': <class 'omni.graph.core._impl.commands.instancing_commands.RemoveOmniGraphAPICommand'>}, 'ChangePipelineStageCommand': {}, 'ChangePipelineStage': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.ChangePipelineStageCommand'>}, 'ChangeVariableTypeCommand': {}, 'ChangeVariableType': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.ChangeVariableTypeCommand'>}, 'ConnectAttrsCommand': {}, 'ConnectAttrs': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.ConnectAttrsCommand'>}, 'ConnectPrimCommand': {}, 'ConnectPrim': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.ConnectPrimCommand'>}, 'CreateAttrCommand': {}, 'CreateAttr': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.CreateAttrCommand'>}, 'CreateGraphAsNodeCommand': {}, 'CreateGraphAsNode': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.CreateGraphAsNodeCommand'>}, 'CreateNodeCommand': {}, 'CreateNode': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.CreateNodeCommand'>}, 'CreateSubgraphCommand': {}, 'CreateSubgraph': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.CreateSubgraphCommand'>}, 'CreateVariableCommand': {}, 'CreateVariable': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.CreateVariableCommand'>}, 'DeleteNodeCommand': {}, 'DeleteNode': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.DeleteNodeCommand'>}, 'DisableGraphCommand': {}, 'DisableGraph': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.DisableGraphCommand'>}, 'DisableGraphUSDHandlerCommand': {}, 'DisableGraphUSDHandler': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.DisableGraphUSDHandlerCommand'>}, 'DisableNodeCommand': {}, 'DisableNode': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.DisableNodeCommand'>}, 'DisconnectAllAttrsCommand': {}, 'DisconnectAllAttrs': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.DisconnectAllAttrsCommand'>}, 'DisconnectAttrsCommand': {}, 'DisconnectAttrs': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.DisconnectAttrsCommand'>}, 'DisconnectPrimCommand': {}, 'DisconnectPrim': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.DisconnectPrimCommand'>}, 'EnableGraphCommand': {}, 'EnableGraph': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.EnableGraphCommand'>}, 'EnableGraphUSDHandlerCommand': {}, 'EnableGraphUSDHandler': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.EnableGraphUSDHandlerCommand'>}, 'EnableNodeCommand': {}, 'EnableNode': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.EnableNodeCommand'>}, 'MapAttrCommand': {}, 'MapAttr': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.MapAttrCommand'>}, 'RemoveAttrCommand': {}, 'RemoveAttr': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.RemoveAttrCommand'>}, 'RemoveVariableCommand': {}, 'RemoveVariable': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.RemoveVariableCommand'>}, 'RenameNodeCommand': {}, 'RenameNode': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.RenameNodeCommand'>}, 'RenameSubgraphCommand': {}, 'RenameSubgraph': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.RenameSubgraphCommand'>}, 'ReplaceWithCompound': {'omni.graph.core._impl.commands.compound_commands': <class 'omni.graph.core._impl.commands.compound_commands.ReplaceWithCompound'>}, 'ResolveAttrTypeCommand': {}, 'ResolveAttrType': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands.ResolveAttrTypeCommand'>}, 'SetAttrCommand': {}, 'SetAttr': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.SetAttrCommand'>}, 'SetAttrDataCommand': {}, 'SetAttrData': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.SetAttrDataCommand'>}, 'SetEvaluationModeCommand': {}, 'SetEvaluationMode': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.SetEvaluationModeCommand'>}, 'SetVariableTooltipCommand': {}, 'SetVariableTooltip': {'omni.graph.core._impl.commands.value_commands': <class 'omni.graph.core._impl.commands.value_commands.SetVariableTooltipCommand'>}, '_OGRestoreConnectionsOnUndo': {'omni.graph.core._impl.commands.topology_commands': <class 'omni.graph.core._impl.commands.topology_commands._OGRestoreConnectionsOnUndo'>}, 'SelectVariantPrimCommand': {}, 'SelectVariantPrim': {'omni.kit.property.usd.variants_model': <class 'omni.kit.property.usd.variants_model.SelectVariantPrimCommand'>}, 'IsaacSimDestroyPrim': {'isaacsim.core.utils.commands': <class 'isaacsim.core.utils.commands.IsaacSimDestroyPrim'>}, 'IsaacSimScalePrim': {'isaacsim.core.utils.commands': <class 'isaacsim.core.utils.commands.IsaacSimScalePrim'>}, 'IsaacSimSpawnPrim': {'isaacsim.core.utils.commands': <class 'isaacsim.core.utils.commands.IsaacSimSpawnPrim'>}, 'IsaacSimTeleportPrim': {'isaacsim.core.utils.commands': <class 'isaacsim.core.utils.commands.IsaacSimTeleportPrim'>}, 'CreateMeshPrimCommand': {}, 'CreateMeshPrim': {'omni.kit.primitive.mesh.command': <class 'omni.kit.primitive.mesh.command.CreateMeshPrimCommand'>}, 'CreateMeshPrimWithDefaultXformCommand': {}, 'CreateMeshPrimWithDefaultXform': {'omni.kit.primitive.mesh.command': <class 'omni.kit.primitive.mesh.command.CreateMeshPrimWithDefaultXformCommand'>}, 'ApplyScriptingAPICommand': {}, 'ApplyScriptingAPI': {'omni.kit.scripting.scripts.command': <class 'omni.kit.scripting.scripts.command.ApplyScriptingAPICommand'>}, 'RemoveScriptingAPICommand': {}, 'RemoveScriptingAPI': {'omni.kit.scripting.scripts.command': <class 'omni.kit.scripting.scripts.command.RemoveScriptingAPICommand'>}, 'RefreshScriptingPropertyWindowCommand': {}, 'RefreshScriptingPropertyWindow': {'omni.kit.scripting.scripts.command': <class 'omni.kit.scripting.scripts.command.RefreshScriptingPropertyWindowCommand'>}, 'URDFCreateImportConfig': {'isaacsim.asset.importer.urdf.scripts.commands': <class 'isaacsim.asset.importer.urdf.scripts.commands.URDFCreateImportConfig'>}, 'URDFImportRobot': {'isaacsim.asset.importer.urdf.scripts.commands': <class 'isaacsim.asset.importer.urdf.scripts.commands.URDFImportRobot'>}, 'URDFParseAndImportFile': {'isaacsim.asset.importer.urdf.scripts.commands': <class 'isaacsim.asset.importer.urdf.scripts.commands.URDFParseAndImportFile'>}, 'URDFParseFile': {'isaacsim.asset.importer.urdf.scripts.commands': <class 'isaacsim.asset.importer.urdf.scripts.commands.URDFParseFile'>}, 'URDFParseText': {'isaacsim.asset.importer.urdf.scripts.commands': <class 'isaacsim.asset.importer.urdf.scripts.commands.URDFParseText'>}, 'AddAnimCurves': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.AddAnimCurves'>}, 'AnimCurveCommandBase': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.AnimCurveCommandBase'>}, 'EditAnimCurveKeys': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.EditAnimCurveKeys'>}, 'ExtractAnimCurves': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.ExtractAnimCurves'>}, 'PasteAnimCurveKeys': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.PasteAnimCurveKeys'>}, 'PasteAnimCurves': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.PasteAnimCurves'>}, 'RemoveAnimCurveKeys': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.RemoveAnimCurveKeys'>}, 'RemoveAnimCurves': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.RemoveAnimCurves'>}, 'SelectAnimCurveKeys': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.SelectAnimCurveKeys'>}, 'SetAnimCurveDefaultTangentType': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.SetAnimCurveDefaultTangentType'>}, 'SetAnimCurveInfinityType': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.SetAnimCurveInfinityType'>}, 'SetAnimCurveKeys': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.SetAnimCurveKeys'>}, 'SimplifyAnimCurves': {'omni.anim.curve.core.scripts.commands': <class 'omni.anim.curve.core.scripts.commands.SimplifyAnimCurves'>}, 'MJCFCreateAsset': {'isaacsim.asset.importer.mjcf.scripts.commands': <class 'isaacsim.asset.importer.mjcf.scripts.commands.MJCFCreateAsset'>}, 'MJCFCreateImportConfig': {'isaacsim.asset.importer.mjcf.scripts.commands': <class 'isaacsim.asset.importer.mjcf.scripts.commands.MJCFCreateImportConfig'>}, 'CreateSurfaceGripper': {'isaacsim.robot.surface_gripper.impl.commands': <class 'isaacsim.robot.surface_gripper.impl.commands.CreateSurfaceGripper'>}, 'IsaacSensorCreateContactSensor': {'isaacsim.sensors.physics.scripts.commands': <class 'isaacsim.sensors.physics.scripts.commands.IsaacSensorCreateContactSensor'>}, 'IsaacSensorCreateImuSensor': {'isaacsim.sensors.physics.scripts.commands': <class 'isaacsim.sensors.physics.scripts.commands.IsaacSensorCreateImuSensor'>}, 'IsaacSensorCreatePrim': {'isaacsim.sensors.physics.scripts.commands': <class 'isaacsim.sensors.physics.scripts.commands.IsaacSensorCreatePrim'>}, 'ToolbarPauseButtonClickedCommand': {}, 'ToolbarPauseButtonClicked': {'omni.kit.widget.toolbar.commands': <class 'omni.kit.widget.toolbar.commands.ToolbarPauseButtonClickedCommand'>}, 'ToolbarPlayButtonClickedCommand': {}, 'ToolbarPlayButtonClicked': {'omni.kit.widget.toolbar.commands': <class 'omni.kit.widget.toolbar.commands.ToolbarPlayButtonClickedCommand'>}, 'ToolbarPlayFilterCheckedCommand': {}, 'ToolbarPlayFilterChecked': {'omni.kit.widget.toolbar.commands': <class 'omni.kit.widget.toolbar.commands.ToolbarPlayFilterCheckedCommand'>}, 'ToolbarPlayFilterSelectAllCommand': {}, 'ToolbarPlayFilterSelectAll': {'omni.kit.widget.toolbar.commands': <class 'omni.kit.widget.toolbar.commands.ToolbarPlayFilterSelectAllCommand'>}, 'ToolbarStopButtonClickedCommand': {}, 'ToolbarStopButtonClicked': {'omni.kit.widget.toolbar.commands': <class 'omni.kit.widget.toolbar.commands.ToolbarStopButtonClickedCommand'>}, 'IsaacSensorCreateLightBeamSensor': {'isaacsim.sensors.physx.scripts.commands': <class 'isaacsim.sensors.physx.scripts.commands.IsaacSensorCreateLightBeamSensor'>}, 'RangeSensorCreateGeneric': {'isaacsim.sensors.physx.scripts.commands': <class 'isaacsim.sensors.physx.scripts.commands.RangeSensorCreateGeneric'>}, 'RangeSensorCreateLidar': {'isaacsim.sensors.physx.scripts.commands': <class 'isaacsim.sensors.physx.scripts.commands.RangeSensorCreateLidar'>}, 'RangeSensorCreatePrim': {'isaacsim.sensors.physx.scripts.commands': <class 'isaacsim.sensors.physx.scripts.commands.RangeSensorCreatePrim'>}, 'IsaacSensorCreateRtxIDS': {'isaacsim.sensors.rtx.scripts.commands': <class 'isaacsim.sensors.rtx.scripts.commands.IsaacSensorCreateRtxIDS'>}, 'IsaacSensorCreateRtxLidar': {'isaacsim.sensors.rtx.scripts.commands': <class 'isaacsim.sensors.rtx.scripts.commands.IsaacSensorCreateRtxLidar'>}, 'IsaacSensorCreateRtxRadar': {'isaacsim.sensors.rtx.scripts.commands': <class 'isaacsim.sensors.rtx.scripts.commands.IsaacSensorCreateRtxRadar'>}, 'TestCppCommand': {}, 'TestCpp': {'omni.fabric.commands': <class 'omni.fabric.commands.TestCppCommand'>}, 'TransformMultiPrimsSRTFabricCpp': {'omni.fabric.commands': <class 'omni.fabric.commands.TransformMultiPrimsSRTFabricCpp'>}, 'USDPhysicsUIUpdateGizmoTransformCommand': {}, 'USDPhysicsUIUpdateGizmoTransform': {'omni.usdphysicsui.scripts.commands': <class 'omni.usdphysicsui.scripts.commands.USDPhysicsUIUpdateGizmoTransformCommand'>}, 'AddCollisionGroupCommand': {}, 'AddCollisionGroup': {'omni.physxcommands': <class 'omni.physxcommands.AddCollisionGroupCommand'>}, 'AddD6PhysicsJointComponentCommand': {}, 'AddD6PhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddD6PhysicsJointComponentCommand'>}, 'AddDeformableBodyComponentCommand': {}, 'AddDeformableBodyComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddDeformableBodyComponentCommand'>}, 'AddDeformableBodyMaterialCommand': {}, 'AddDeformableBodyMaterial': {'omni.physxcommands': <class 'omni.physxcommands.AddDeformableBodyMaterialCommand'>}, 'AddDeformableSurfaceComponentCommand': {}, 'AddDeformableSurfaceComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddDeformableSurfaceComponentCommand'>}, 'AddDeformableSurfaceMaterialCommand': {}, 'AddDeformableSurfaceMaterial': {'omni.physxcommands': <class 'omni.physxcommands.AddDeformableSurfaceMaterialCommand'>}, 'AddDiffuseParticlesCommand': {}, 'AddDiffuseParticles': {'omni.physxcommands': <class 'omni.physxcommands.AddDiffuseParticlesCommand'>}, 'AddDistancePhysicsJointComponentCommand': {}, 'AddDistancePhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddDistancePhysicsJointComponentCommand'>}, 'AddFixedPhysicsJointComponentCommand': {}, 'AddFixedPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddFixedPhysicsJointComponentCommand'>}, 'AddGroundPlaneCommand': {}, 'AddGroundPlane': {'omni.physxcommands': <class 'omni.physxcommands.AddGroundPlaneCommand'>}, 'AddHairCommand': {}, 'AddHair': {'omni.physxcommands': <class 'omni.physxcommands.AddHairCommand'>}, 'AddHairMaterialCommand': {}, 'AddHairMaterial': {'omni.physxcommands': <class 'omni.physxcommands.AddHairMaterialCommand'>}, 'AddPBDMaterialCommand': {}, 'AddPBDMaterial': {'omni.physxcommands': <class 'omni.physxcommands.AddPBDMaterialCommand'>}, 'AddPairFilterCommand': {}, 'AddPairFilter': {'omni.physxcommands': <class 'omni.physxcommands.AddPairFilterCommand'>}, 'AddParticleAnisotropyCommand': {}, 'AddParticleAnisotropy': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleAnisotropyCommand'>}, 'AddParticleClothComponentCommand': {}, 'AddParticleClothComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleClothComponentCommand'>}, 'AddParticleIsosurfaceCommand': {}, 'AddParticleIsosurface': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleIsosurfaceCommand'>}, 'AddParticleSamplingCommand': {}, 'AddParticleSampling': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleSamplingCommand'>}, 'AddParticleSetCommand': {}, 'AddParticleSet': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleSetCommand'>}, 'AddParticleSmoothingCommand': {}, 'AddParticleSmoothing': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleSmoothingCommand'>}, 'AddParticleSystemCommand': {}, 'AddParticleSystem': {'omni.physxcommands': <class 'omni.physxcommands.AddParticleSystemCommand'>}, 'AddPhysicsComponentCommand': {}, 'AddPhysicsComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddPhysicsComponentCommand'>}, 'AddPhysicsSceneCommand': {}, 'AddPhysicsScene': {'omni.physxcommands': <class 'omni.physxcommands.AddPhysicsSceneCommand'>}, 'AddPrismaticPhysicsJointComponentCommand': {}, 'AddPrismaticPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddPrismaticPhysicsJointComponentCommand'>}, 'AddRevolutePhysicsJointComponentCommand': {}, 'AddRevolutePhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddRevolutePhysicsJointComponentCommand'>}, 'AddRigidBodyMaterialCommand': {}, 'AddRigidBodyMaterial': {'omni.physxcommands': <class 'omni.physxcommands.AddRigidBodyMaterialCommand'>}, 'AddSphericalPhysicsJointComponentCommand': {}, 'AddSphericalPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.AddSphericalPhysicsJointComponentCommand'>}, 'ApplyAPISchemaCommand': {}, 'ApplyAPISchema': {'omni.physxcommands': <class 'omni.physxcommands.ApplyAPISchemaCommand'>}, 'ChangeAttributeCommand': {}, 'ChangeAttribute': {'omni.physxcommands': <class 'omni.physxcommands.ChangeAttributeCommand'>}, 'CookPhysxColliders': {'omni.physxcommands': <class 'omni.physxcommands.CookPhysxColliders'>}, 'CreateJointCommand': {}, 'CreateJoint': {'omni.physxcommands': <class 'omni.physxcommands.CreateJointCommand'>}, 'CreateJointsCommand': {}, 'CreateJoints': {'omni.physxcommands': <class 'omni.physxcommands.CreateJointsCommand'>}, 'CreatePhysicsAttachmentCommand': {}, 'CreatePhysicsAttachment': {'omni.physxcommands': <class 'omni.physxcommands.CreatePhysicsAttachmentCommand'>}, 'DeletePrimsCommand': {}, 'DeletePrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.DeletePrimsCommand'>}, 'ImportTetrahedralMeshCommand': {}, 'ImportTetrahedralMesh': {'omni.physxcommands': <class 'omni.physxcommands.ImportTetrahedralMeshCommand'>}, 'PhysicsCommand': {}, 'Physics': {'omni.physxcommands': <class 'omni.physxcommands.PhysicsCommand'>}, 'RemoveAttributeCommand': {}, 'RemoveAttribute': {'omni.physxcommands': <class 'omni.physxcommands.RemoveAttributeCommand'>}, 'RemoveD6PhysicsJointComponentCommand': {}, 'RemoveD6PhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveD6PhysicsJointComponentCommand'>}, 'RemoveDeformableBodyComponentCommand': {}, 'RemoveDeformableBodyComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveDeformableBodyComponentCommand'>}, 'RemoveDeformableSurfaceComponentCommand': {}, 'RemoveDeformableSurfaceComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveDeformableSurfaceComponentCommand'>}, 'RemoveDistancePhysicsJointComponentCommand': {}, 'RemoveDistancePhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveDistancePhysicsJointComponentCommand'>}, 'RemoveFixedPhysicsJointComponentCommand': {}, 'RemoveFixedPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveFixedPhysicsJointComponentCommand'>}, 'RemoveHairCommand': {}, 'RemoveHair': {'omni.physxcommands': <class 'omni.physxcommands.RemoveHairCommand'>}, 'RemovePairFilterCommand': {}, 'RemovePairFilter': {'omni.physxcommands': <class 'omni.physxcommands.RemovePairFilterCommand'>}, 'RemoveParticleClothComponentCommand': {}, 'RemoveParticleClothComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveParticleClothComponentCommand'>}, 'RemoveParticleSamplingCommand': {}, 'RemoveParticleSampling': {'omni.physxcommands': <class 'omni.physxcommands.RemoveParticleSamplingCommand'>}, 'RemoveParticleSetCommand': {}, 'RemoveParticleSet': {'omni.physxcommands': <class 'omni.physxcommands.RemoveParticleSetCommand'>}, 'RemovePhysicsComponentCommand': {}, 'RemovePhysicsComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemovePhysicsComponentCommand'>}, 'RemovePrismaticPhysicsJointComponentCommand': {}, 'RemovePrismaticPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemovePrismaticPhysicsJointComponentCommand'>}, 'RemoveRelationshipCommand': {}, 'RemoveRelationship': {'omni.physxcommands': <class 'omni.physxcommands.RemoveRelationshipCommand'>}, 'RemoveRevolutePhysicsJointComponentCommand': {}, 'RemoveRevolutePhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveRevolutePhysicsJointComponentCommand'>}, 'RemoveRigidBodyCommand': {}, 'RemoveRigidBody': {'omni.physxcommands': <class 'omni.physxcommands.RemoveRigidBodyCommand'>}, 'RemoveSpatialTendonAttachmentAPICommand': {}, 'RemoveSpatialTendonAttachmentAPI': {'omni.physxcommands': <class 'omni.physxcommands.RemoveSpatialTendonAttachmentAPICommand'>}, 'RemoveSphericalPhysicsJointComponentCommand': {}, 'RemoveSphericalPhysicsJointComponent': {'omni.physxcommands': <class 'omni.physxcommands.RemoveSphericalPhysicsJointComponentCommand'>}, 'RemoveStaticColliderCommand': {}, 'RemoveStaticCollider': {'omni.physxcommands': <class 'omni.physxcommands.RemoveStaticColliderCommand'>}, 'RemoveTendonComponentsCommand': {}, 'RemoveTendonComponents': {'omni.physxcommands': <class 'omni.physxcommands.RemoveTendonComponentsCommand'>}, 'SetCustomMetadataCommand': {}, 'SetCustomMetadata': {'omni.physxcommands': <class 'omni.physxcommands.SetCustomMetadataCommand'>}, 'SetRigidBodyCommand': {}, 'SetRigidBody': {'omni.physxcommands': <class 'omni.physxcommands.SetRigidBodyCommand'>}, 'SetSpatialTendonAttachmentParentCommand': {}, 'SetSpatialTendonAttachmentParent': {'omni.physxcommands': <class 'omni.physxcommands.SetSpatialTendonAttachmentParentCommand'>}, 'SetStaticColliderCommand': {}, 'SetStaticCollider': {'omni.physxcommands': <class 'omni.physxcommands.SetStaticColliderCommand'>}, 'UnapplyAPISchemaCommand': {}, 'UnapplyAPISchema': {'omni.physxcommands': <class 'omni.physxcommands.UnapplyAPISchemaCommand'>}, 'ClearPhysicsComponentsCommand': {}, 'ClearPhysicsComponents': {'omni.physxui.scripts.commands': <class 'omni.physxui.scripts.commands.ClearPhysicsComponentsCommand'>}, 'AddXformOpCommand': {}, 'AddXformOp': {'omni.kit.property.transform.scripts.transform_commands': <class 'omni.kit.property.transform.scripts.transform_commands.AddXformOpCommand'>}, 'ChangeRotationOpCommand': {}, 'ChangeRotationOp': {'omni.kit.property.transform.scripts.transform_commands': <class 'omni.kit.property.transform.scripts.transform_commands.ChangeRotationOpCommand'>}, 'EnableXformOpCommand': {}, 'EnableXformOp': {'omni.kit.property.transform.scripts.transform_commands': <class 'omni.kit.property.transform.scripts.transform_commands.EnableXformOpCommand'>}, 'RemoveXformOpAndAttrbuteCommand': {}, 'RemoveXformOpAndAttrbute': {'omni.kit.property.transform.scripts.transform_commands': <class 'omni.kit.property.transform.scripts.transform_commands.RemoveXformOpAndAttrbuteCommand'>}, 'RemoveXformOpCommand': {}, 'RemoveXformOp': {'omni.kit.property.transform.scripts.transform_commands': <class 'omni.kit.property.transform.scripts.transform_commands.RemoveXformOpCommand'>}, 'PhysXVehicleAddArrayEntryCommand': {}, 'PhysXVehicleAddArrayEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleAddArrayEntryCommand'>}, 'PhysXVehicleChangeArrayEntryCommand': {}, 'PhysXVehicleChangeArrayEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleChangeArrayEntryCommand'>}, 'PhysXVehicleCommandResponseChangeEntryCommand': {}, 'PhysXVehicleCommandResponseChangeEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleCommandResponseChangeEntryCommand'>}, 'PhysXVehicleControllerEnableAutoReverseCommand': {}, 'PhysXVehicleControllerEnableAutoReverse': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleControllerEnableAutoReverseCommand'>}, 'PhysXVehicleControllerEnableInputCommand': {}, 'PhysXVehicleControllerEnableInput': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleControllerEnableInputCommand'>}, 'PhysXVehicleControllerEnableMouseCommand': {}, 'PhysXVehicleControllerEnableMouse': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleControllerEnableMouseCommand'>}, 'PhysXVehicleControllerSetSteeringFilterTimeCommand': {}, 'PhysXVehicleControllerSetSteeringFilterTime': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleControllerSetSteeringFilterTimeCommand'>}, 'PhysXVehicleControllerSetSteeringSensitivityCommand': {}, 'PhysXVehicleControllerSetSteeringSensitivity': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleControllerSetSteeringSensitivityCommand'>}, 'PhysXVehicleDifferentialChangeEntryCommand': {}, 'PhysXVehicleDifferentialChangeEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleDifferentialChangeEntryCommand'>}, 'PhysXVehicleRemoveArrayEntryCommand': {}, 'PhysXVehicleRemoveArrayEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleRemoveArrayEntryCommand'>}, 'PhysXVehicleSetArrayEntryCommand': {}, 'PhysXVehicleSetArrayEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleSetArrayEntryCommand'>}, 'PhysXVehicleSetRelationshipCommand': {}, 'PhysXVehicleSetRelationship': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleSetRelationshipCommand'>}, 'PhysXVehicleSuspensionFrameTransformsAutocomputeCommand': {}, 'PhysXVehicleSuspensionFrameTransformsAutocompute': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleSuspensionFrameTransformsAutocomputeCommand'>}, 'PhysXVehicleTireFrictionTableAddCommand': {}, 'PhysXVehicleTireFrictionTableAdd': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleTireFrictionTableAddCommand'>}, 'PhysXVehicleTireFrictionTableAddEntryCommand': {}, 'PhysXVehicleTireFrictionTableAddEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleTireFrictionTableAddEntryCommand'>}, 'PhysXVehicleTireFrictionTableChangeEntryCommand': {}, 'PhysXVehicleTireFrictionTableChangeEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleTireFrictionTableChangeEntryCommand'>}, 'PhysXVehicleTireFrictionTableRemoveEntryCommand': {}, 'PhysXVehicleTireFrictionTableRemoveEntry': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleTireFrictionTableRemoveEntryCommand'>}, 'PhysXVehicleWizardCreateCommand': {}, 'PhysXVehicleWizardCreate': {'omni.physxvehicle.scripts.commands': <class 'omni.physxvehicle.scripts.commands.PhysXVehicleWizardCreateCommand'>}, 'DuplicateCameraCommand': {}, 'DuplicateCamera': {'omni.kit.viewport.menubar.camera.commands': <class 'omni.kit.viewport.menubar.camera.commands.DuplicateCameraCommand'>}, 'DuplicateViewportCameraCommand': {}, 'DuplicateViewportCamera': {'omni.kit.viewport.menubar.camera.commands': <class 'omni.kit.viewport.menubar.camera.commands.DuplicateViewportCameraCommand'>}, 'SetViewportCameraCommand': {}, 'SetViewportCamera': {'omni.kit.viewport.menubar.camera.commands': <class 'omni.kit.viewport.menubar.camera.commands.SetViewportCameraCommand'>}, 'SetLightingMenuModeCommand': {}, 'SetLightingMenuMode': {'omni.kit.viewport.menubar.lighting.commands': <class 'omni.kit.viewport.menubar.lighting.commands.SetLightingMenuModeCommand'>}, 'PhysXAddDroneCameraCommand': {}, 'PhysXAddDroneCamera': {'omni.physxcamera.scripts.commands': <class 'omni.physxcamera.scripts.commands.PhysXAddDroneCameraCommand'>}, 'PhysXAddFollowLookCameraCommand': {}, 'PhysXAddFollowLookCamera': {'omni.physxcamera.scripts.commands': <class 'omni.physxcamera.scripts.commands.PhysXAddFollowLookCameraCommand'>}, 'PhysXAddFollowVelocityCameraCommand': {}, 'PhysXAddFollowVelocityCamera': {'omni.physxcamera.scripts.commands': <class 'omni.physxcamera.scripts.commands.PhysXAddFollowVelocityCameraCommand'>}, 'RestoreDefaultRenderSettingCommand': {}, 'RestoreDefaultRenderSetting': {'omni.rtx.window.settings.commands': <class 'omni.rtx.window.settings.commands.RestoreDefaultRenderSettingCommand'>}, 'RestoreDefaultRenderSettingSectionCommand': {}, 'RestoreDefaultRenderSettingSection': {'omni.rtx.window.settings.commands': <class 'omni.rtx.window.settings.commands.RestoreDefaultRenderSettingSectionCommand'>}, 'SetCurrentRenderer': {'omni.rtx.window.settings.commands': <class 'omni.rtx.window.settings.commands.SetCurrentRenderer'>}, 'SetCurrentStack': {'omni.rtx.window.settings.commands': <class 'omni.rtx.window.settings.commands.SetCurrentStack'>}, 'Command': {}, '': {'omni.kit.commands.command': <class 'omni.kit.commands.command.Command'>}, 'ChangeSettingCommand': {}, 'ChangeSetting': {'omni.kit.commands.builtin.settings_commands': <class 'omni.kit.commands.builtin.settings_commands.ChangeSettingCommand'>}, 'ChangeDraggableSettingCommand': {}, 'ChangeDraggableSetting': {'omni.kit.commands.builtin.settings_commands': <class 'omni.kit.commands.builtin.settings_commands.ChangeDraggableSettingCommand'>}, 'GroupPrimsCommand': {}, 'GroupPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.GroupPrimsCommand'>}, 'UngroupPrimsCommand': {}, 'UngroupPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.UngroupPrimsCommand'>}, 'CreatePrimWithDefaultXformCommand': {}, 'CreatePrimWithDefaultXform': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePrimWithDefaultXformCommand'>}, 'CreatePrimCommand': {}, 'CreatePrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePrimCommand'>}, 'CopyPrimCommand': {}, 'CopyPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CopyPrimCommand'>}, 'CopyPrimsCommand': {}, 'CopyPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CopyPrimsCommand'>}, 'CreateInstanceCommand': {}, 'CreateInstance': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateInstanceCommand'>}, 'CreateInstancesCommand': {}, 'CreateInstances': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateInstancesCommand'>}, 'CreatePrimsCommand': {}, 'CreatePrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePrimsCommand'>}, 'CreateDefaultXformOnPrimCommand': {}, 'CreateDefaultXformOnPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateDefaultXformOnPrimCommand'>}, 'BindMaterialCommand': {}, 'BindMaterial': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.BindMaterialCommand'>}, 'SetMaterialStrengthCommand': {}, 'SetMaterialStrength': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.SetMaterialStrengthCommand'>}, 'TransformPrimCommand': {}, 'TransformPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.TransformPrimCommand'>}, 'TransformPrimSRTCommand': {}, 'TransformPrimSRT': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.TransformPrimSRTCommand'>}, 'TransformPrimsCommand': {}, 'TransformPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.TransformPrimsCommand'>}, 'TransformPrimsSRTCommand': {}, 'TransformPrimsSRT': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.TransformPrimsSRTCommand'>}, 'FramePrimsCommand': {}, 'FramePrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.FramePrimsCommand'>}, 'SelectPrimsCommand': {}, 'SelectPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.SelectPrimsCommand'>}, 'ToggleVisibilitySelectedPrimsCommand': {}, 'ToggleVisibilitySelectedPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ToggleVisibilitySelectedPrimsCommand'>}, 'UnhideAllPrimsCommand': {}, 'UnhideAllPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.UnhideAllPrimsCommand'>}, 'MovePrimCommand': {}, 'MovePrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.MovePrimCommand'>}, 'MovePrimsCommand': {}, 'MovePrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.MovePrimsCommand'>}, 'RenamePrimCommand': {}, 'RenamePrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RenamePrimCommand'>}, 'ReplaceReferencesCommand': {}, 'ReplaceReferences': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ReplaceReferencesCommand'>}, 'CreateUsdAttributeOnPathCommand': {}, 'CreateUsdAttributeOnPath': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateUsdAttributeOnPathCommand'>}, 'CreateUsdAttributeCommand': {}, 'CreateUsdAttribute': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateUsdAttributeCommand'>}, 'ChangePropertyCommand': {}, 'ChangeProperty': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ChangePropertyCommand'>}, 'SetUsdShadeInfoAttributeCommand': {}, 'SetUsdShadeInfoAttribute': {'omni.kit.property.material.scripts.commands.set_info_attribute_command': <class 'omni.kit.property.material.scripts.commands.set_info_attribute_command.SetUsdShadeInfoAttributeCommand'>}, 'RemovePropertyCommand': {}, 'RemoveProperty': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RemovePropertyCommand'>}, 'ChangeMetadataInPrimsCommand': {}, 'ChangeMetadataInPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ChangeMetadataInPrimsCommand'>}, 'ChangeMetadataCommand': {}, 'ChangeMetadata': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ChangeMetadataCommand'>}, 'ChangeAttributesColorSpaceCommand': {}, 'ChangeAttributesColorSpace': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ChangeAttributesColorSpaceCommand'>}, 'CreateMdlMaterialPrimCommand': {}, 'CreateMdlMaterialPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateMdlMaterialPrimCommand'>}, 'CreateMtlxMaterialPrimCommand': {}, 'CreateMtlxMaterialPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateMtlxMaterialPrimCommand'>}, 'CreateShaderPrimFromSdrCommand': {}, 'CreateShaderPrimFromSdr': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateShaderPrimFromSdrCommand'>}, 'CreatePreviewSurfaceMaterialPrimCommand': {}, 'CreatePreviewSurfaceMaterialPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePreviewSurfaceMaterialPrimCommand'>}, 'CreatePreviewSurfaceTextureMaterialPrimCommand': {}, 'CreatePreviewSurfaceTextureMaterialPrim': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePreviewSurfaceTextureMaterialPrimCommand'>}, 'ClearCurvesSplitsOverridesCommand': {}, 'ClearCurvesSplitsOverrides': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ClearCurvesSplitsOverridesCommand'>}, 'ClearRefinementOverridesCommand': {}, 'ClearRefinementOverrides': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ClearRefinementOverridesCommand'>}, 'RelationshipTargetBase': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RelationshipTargetBase'>}, 'AddRelationshipTargetCommand': {}, 'AddRelationshipTarget': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.AddRelationshipTargetCommand'>}, 'RemoveRelationshipTargetCommand': {}, 'RemoveRelationshipTarget': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RemoveRelationshipTargetCommand'>}, 'SetRelationshipTargetsCommand': {}, 'SetRelationshipTargets': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.SetRelationshipTargetsCommand'>}, 'ReferenceCommandBase': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ReferenceCommandBase'>}, 'AddReferenceCommand': {}, 'AddReference': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.AddReferenceCommand'>}, 'RemoveReferenceCommand': {}, 'RemoveReference': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RemoveReferenceCommand'>}, 'ReplaceReferenceCommand': {}, 'ReplaceReference': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ReplaceReferenceCommand'>}, 'PayloadCommandBase': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.PayloadCommandBase'>}, 'AddPayloadCommand': {}, 'AddPayload': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.AddPayloadCommand'>}, 'RemovePayloadCommand': {}, 'RemovePayload': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.RemovePayloadCommand'>}, 'ReplacePayloadCommand': {}, 'ReplacePayload': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ReplacePayloadCommand'>}, 'CreatePrimCommandBase': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePrimCommandBase'>}, 'CreateReferenceCommand': {}, 'CreateReference': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateReferenceCommand'>}, 'CreatePayloadCommand': {}, 'CreatePayload': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreatePayloadCommand'>}, 'CreateAudioPrimFromAssetPathCommand': {}, 'CreateAudioPrimFromAssetPath': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.CreateAudioPrimFromAssetPathCommand'>}, 'ToggleActivePrimsCommand': {}, 'ToggleActivePrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ToggleActivePrimsCommand'>}, 'TogglePayLoadLoadSelectedPrimsCommand': {}, 'TogglePayLoadLoadSelectedPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.TogglePayLoadLoadSelectedPrimsCommand'>}, 'SetPayLoadLoadSelectedPrimsCommand': {}, 'SetPayLoadLoadSelectedPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.SetPayLoadLoadSelectedPrimsCommand'>}, 'ParentPrimsCommand': {}, 'ParentPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.ParentPrimsCommand'>}, 'UnparentPrimsCommand': {}, 'UnparentPrims': {'omni.usd.commands.usd_commands': <class 'omni.usd.commands.usd_commands.UnparentPrimsCommand'>}, 'AbstractLayerCommand': {}, 'AbstractLayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.AbstractLayerCommand'>}, 'SetEditTargetCommand': {}, 'SetEditTarget': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.SetEditTargetCommand'>}, 'CreateSublayerCommand': {}, 'CreateSublayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.CreateSublayerCommand'>}, 'RemoveSublayerCommand': {}, 'RemoveSublayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.RemoveSublayerCommand'>}, 'MergeLayersCommand': {}, 'MergeLayers': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.MergeLayersCommand'>}, 'FlattenLayersCommand': {}, 'FlattenLayers': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.FlattenLayersCommand'>}, 'CreateLayerReferenceCommand': {}, 'CreateLayerReference': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.CreateLayerReferenceCommand'>}, 'StitchPrimSpecsToLayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.StitchPrimSpecsToLayer'>}, 'MovePrimSpecsToLayerCommand': {}, 'MovePrimSpecsToLayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.MovePrimSpecsToLayerCommand'>}, 'MoveSublayerCommand': {}, 'MoveSublayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.MoveSublayerCommand'>}, 'ReplaceSublayerCommand': {}, 'ReplaceSublayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.ReplaceSublayerCommand'>}, 'SetLayerMutenessCommand': {}, 'SetLayerMuteness': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.SetLayerMutenessCommand'>}, 'LockLayerCommand': {}, 'LockLayer': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.LockLayerCommand'>}, 'LinkSpecsCommand': {}, 'LinkSpecs': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.LinkSpecsCommand'>}, 'UnlinkSpecsCommand': {}, 'UnlinkSpecs': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.UnlinkSpecsCommand'>}, 'LockSpecsCommand': {}, 'LockSpecs': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.LockSpecsCommand'>}, 'UnlockSpecsCommand': {}, 'UnlockSpecs': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.UnlockSpecsCommand'>}, 'RemovePrimSpecCommand': {}, 'RemovePrimSpec': {'omni.kit.usd.layers._impl.layer_commands': <class 'omni.kit.usd.layers._impl.layer_commands.RemovePrimSpecCommand'>}, 'CreateAndBindMdlMaterialFromLibrary': {'omni.kit.material.library.material_library': <class 'omni.kit.material.library.material_library.CreateAndBindMdlMaterialFromLibrary'>}, 'CreateAndBindMtlxSurfaceFromLibrary': {'omni.kit.material.library.material_library': <class 'omni.kit.material.library.material_library.CreateAndBindMtlxSurfaceFromLibrary'>}, 'CreateAndBindPreviewSurfaceFromLibrary': {'omni.kit.material.library.material_library': <class 'omni.kit.material.library.material_library.CreateAndBindPreviewSurfaceFromLibrary'>}, 'CreateAndBindPreviewSurfaceTextureFromLibrary': {'omni.kit.material.library.material_library': <class 'omni.kit.material.library.material_library.CreateAndBindPreviewSurfaceTextureFromLibrary'>}, 'ChangePrimDisplayNameCommand': {}, 'ChangePrimDisplayName': {'omni.kit.widget.stage.commands': <class 'omni.kit.widget.stage.commands.ChangePrimDisplayNameCommand'>}, 'ReorderPrimCommand': {}, 'ReorderPrim': {'omni.kit.widget.stage.commands': <class 'omni.kit.widget.stage.commands.ReorderPrimCommand'>}, 'ToggleExtension': {'omni.kit.window.extensions.ext_commands': <class 'omni.kit.window.extensions.ext_commands.ToggleExtension'>}, 'CreateUsdUIBackdropCommand': {}, 'CreateUsdUIBackdrop': {'omni.kit.graph.usd.commands.commands': <class 'omni.kit.graph.usd.commands.commands.CreateUsdUIBackdropCommand'>}, 'CreateUsdUINoteCommand': {}, 'CreateUsdUINote': {'omni.kit.graph.usd.commands.commands': <class 'omni.kit.graph.usd.commands.commands.CreateUsdUINoteCommand'>}, 'UsdUINodeGraphNodeSetCommand': {}, 'UsdUINodeGraphNodeSet': {'omni.kit.graph.usd.commands.commands': <class 'omni.kit.graph.usd.commands.commands.UsdUINodeGraphNodeSetCommand'>}, 'UsdUIRemovePositionCommand': {}, 'UsdUIRemovePosition': {'omni.kit.graph.usd.commands.commands': <class 'omni.kit.graph.usd.commands.commands.UsdUIRemovePositionCommand'>}, 'NewUsdShadeMaterialCommand': {}, 'NewUsdShadeMaterial': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.NewUsdShadeMaterialCommand'>}, 'NewUsdShadeNodeGraphCommand': {}, 'NewUsdShadeNodeGraph': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.NewUsdShadeNodeGraphCommand'>}, 'NewUsdShadeNodeCommand': {}, 'NewUsdShadeNode': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.NewUsdShadeNodeCommand'>}, 'NewUsdShadeNodeFromSdrCommand': {}, 'NewUsdShadeNodeFromSdr': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.NewUsdShadeNodeFromSdrCommand'>}, 'ConnectUsdShadeToSourceCommand': {}, 'ConnectUsdShadeToSource': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.ConnectUsdShadeToSourceCommand'>}, 'UsdShadeDisconnectSourceCommand': {}, 'UsdShadeDisconnectSource': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.UsdShadeDisconnectSourceCommand'>}, 'ImportCompoundCommand': {}, 'ImportCompound': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.ImportCompoundCommand'>}, 'CreateAbstractPortCommand': {}, 'CreateAbstractPort': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.CreateAbstractPortCommand'>}, 'CreateInputPortCommand': {}, 'CreateInputPort': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.CreateInputPortCommand'>}, 'CreateOutputPortCommand': {}, 'CreateOutputPort': {'omni.kit.window.material_graph.usdshade_commands': <class 'omni.kit.window.material_graph.usdshade_commands.CreateOutputPortCommand'>}, 'SelectAllCommand': {}, 'SelectAll': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectAllCommand'>}, 'SelectNoneCommand': {}, 'SelectNone': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectNoneCommand'>}, 'SelectInvertCommand': {}, 'SelectInvert': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectInvertCommand'>}, 'HideUnselectedCommand': {}, 'HideUnselected': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.HideUnselectedCommand'>}, 'SelectParentCommand': {}, 'SelectParent': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectParentCommand'>}, 'SelectLeafCommand': {}, 'SelectLeaf': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectLeafCommand'>}, 'SelectHierarchyCommand': {}, 'SelectHierarchy': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectHierarchyCommand'>}, 'SelectSimilarCommand': {}, 'SelectSimilar': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectSimilarCommand'>}, 'SelectListCommand': {}, 'SelectList': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectListCommand'>}, 'SelectKindCommand': {}, 'SelectKind': {'omni.kit.selection.selection': <class 'omni.kit.selection.selection.SelectKindCommand'>}, 'DeleteFabricPrimsCommand': {}, 'DeleteFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.DeleteFabricPrimsCommand'>}, 'MoveFabricPrimCommand': {}, 'MoveFabricPrim': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.MoveFabricPrimCommand'>}, 'MoveFabricPrimsCommand': {}, 'MoveFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.MoveFabricPrimsCommand'>}, 'ToggleVisibilitySelectedFabricPrimsCommand': {}, 'ToggleVisibilitySelectedFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.ToggleVisibilitySelectedFabricPrimsCommand'>}, 'CreateFabricPrimWithDefaultXformCommand': {}, 'CreateFabricPrimWithDefaultXform': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CreateFabricPrimWithDefaultXformCommand'>}, 'CreateFabricPrimCommand': {}, 'CreateFabricPrim': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CreateFabricPrimCommand'>}, 'CreateFabricPrimsCommand': {}, 'CreateFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CreateFabricPrimsCommand'>}, 'CreateFabricMeshPrimWithDefaultXform': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CreateFabricMeshPrimWithDefaultXform'>}, 'CreateDefaultXformOnFabricPrimCommand': {}, 'CreateDefaultXformOnFabricPrim': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CreateDefaultXformOnFabricPrimCommand'>}, 'CopyFabricPrimCommand': {}, 'CopyFabricPrim': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CopyFabricPrimCommand'>}, 'CopyFabricPrimsCommand': {}, 'CopyFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.CopyFabricPrimsCommand'>}, 'GroupFabricPrimsCommand': {}, 'GroupFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.GroupFabricPrimsCommand'>}, 'UngroupFabricPrimsCommand': {}, 'UngroupFabricPrims': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.UngroupFabricPrimsCommand'>}, 'TransformFabricPrimCommand': {}, 'TransformFabricPrim': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.TransformFabricPrimCommand'>}, 'ChangeFabricPropertyCommand': {}, 'ChangeFabricProperty': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.ChangeFabricPropertyCommand'>}, 'ChangeFabricAttributeCommand': {}, 'ChangeFabricAttribute': {'omni.fabric.commands.scripts.commands': <class 'omni.fabric.commands.scripts.commands.ChangeFabricAttributeCommand'>}, 'TransformMultiPrimsFabricSRT': {'omni.kit.manipulator.prim.fabric.commands': <class 'omni.kit.manipulator.prim.fabric.commands.TransformMultiPrimsFabricSRT'>}, 'PrimVarCommand': {}, 'PrimVar': {'omni.kit.property.geometry.scripts.geometry_commands': <class 'omni.kit.property.geometry.scripts.geometry_commands.PrimVarCommand'>}, 'TogglePrimVarCommand': {}, 'TogglePrimVar': {'omni.kit.property.geometry.scripts.geometry_commands': <class 'omni.kit.property.geometry.scripts.geometry_commands.TogglePrimVarCommand'>}, 'ToggleInstanceableCommand': {}, 'ToggleInstanceable': {'omni.kit.property.geometry.scripts.geometry_commands': <class 'omni.kit.property.geometry.scripts.geometry_commands.ToggleInstanceableCommand'>}, 'ApplyMassAPICommand': {}, 'ApplyMassAPI': {'omni.physxui.scripts.physicsViewportMassEdit': <class 'omni.physxui.scripts.physicsViewportMassEdit.PhysicsMassDistributionEditManipulator.ApplyMassAPICommand'>}, 'UsdShadeDisconnectCommand': {}, 'UsdShadeDisconnect': {'omni.kit.property.material.scripts.commands.disconnect_command': <class 'omni.kit.property.material.scripts.commands.disconnect_command.UsdShadeDisconnectCommand'>}, 'CreateCollidersCommand': {}, 'CreateColliders': {'omni.physxsupportui.scripts.action_bar': <class 'omni.physxsupportui.scripts.action_bar.CreateCollidersCommand'>}})
