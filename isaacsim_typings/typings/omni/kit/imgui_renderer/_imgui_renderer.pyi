from __future__ import annotations
import omni.appwindow._appwindow
__all__ = ['IImGuiRenderer', 'acquire_imgui_renderer_interface', 'release_imgui_renderer_interface']
class IImGuiRenderer:
    def attach_app_window(self, arg0: omni.appwindow._appwindow.IAppWindow) -> bool:
        ...
    def attach_app_window_with_imgui_context(self, arg0: omni.appwindow._appwindow.IAppWindow, arg1: ...) -> bool:
        ...
    def clear_cursor_shape_override(self, arg0: omni.appwindow._appwindow.IAppWindow) -> None:
        ...
    def detach_app_window(self, arg0: omni.appwindow._appwindow.IAppWindow) -> None:
        ...
    def get_all_cursor_shape_names(self) -> list[str]:
        ...
    def get_cursor_shape_override(self, arg0: omni.appwindow._appwindow.IAppWindow) -> ...:
        ...
    def get_cursor_shape_override_extend(self, arg0: omni.appwindow._appwindow.IAppWindow) -> ...:
        ...
    def get_window_set(self, arg0: omni.appwindow._appwindow.IAppWindow) -> ...:
        ...
    def has_cursor_shape_override(self, arg0: omni.appwindow._appwindow.IAppWindow) -> bool:
        ...
    def is_app_window_attached(self, arg0: omni.appwindow._appwindow.IAppWindow) -> bool:
        ...
    def register_cursor_shape_extend(self, arg0: str, arg1: str) -> None:
        ...
    def set_cursor_shape_override(self, arg0: omni.appwindow._appwindow.IAppWindow, arg1: ...) -> None:
        ...
    def set_cursor_shape_override_extend(self, arg0: omni.appwindow._appwindow.IAppWindow, arg1: str) -> None:
        ...
    def shutdown(self) -> None:
        ...
    def startup(self) -> None:
        ...
    def unregister_cursor_shape_extend(self, arg0: str) -> None:
        ...
def acquire_imgui_renderer_interface(plugin_name: str = None, library_path: str = None) -> IImGuiRenderer:
    ...
def release_imgui_renderer_interface(arg0: IImGuiRenderer) -> None:
    ...
