from __future__ import annotations
from omni import ui
import omni.ui.color_utils
import pathlib
from pathlib import Path
import typing
__all__ = ['CURRENT_PATH', 'Colors', 'ICON_PATH', 'Path', 'UI_STYLES', 'cl', 'ui']
class Colors:
    Background: typing.ClassVar[str] = 'shade:4280492319;light=4283650900'
    Branch: typing.ClassVar[str] = 'shade:4284177243'
    DarkText: typing.ClassVar[str] = 'shade:4283453264;light=4288782753'
    Expand: typing.ClassVar[str] = 'shade:4289045925'
    Image: typing.ClassVar[str] = 'shade:4289243304'
    Scrollbar: typing.ClassVar[str] = 'shade:4286611584;light=4288585374'
    Selected: typing.ClassVar[str] = 'shade:4294952756;light=4291137818'
    Text: typing.ClassVar[str] = 'shade:4288782753;light=4292927712'
CURRENT_PATH: pathlib.PosixPath  # value = PosixPath('/home/<USER>/isaacsim/extscache/omni.kit.browser.core-2.3.11/omni/kit/browser/core/widgets')
ICON_PATH: pathlib.PosixPath  # value = PosixPath('/home/<USER>/isaacsim/extscache/omni.kit.browser.core-2.3.11/icons')
UI_STYLES: dict  # value = {'CollectionList': {'background_color': 'shade:4280492319;light=4283650900', 'selected_color': 'shade:4283189827;light=4282927176', 'color': 'shade:4288782753;light=4292927712', 'border_radius': 1}, 'TreeView': {'background_color': 0, 'background_selected_color': 4285427310}, 'TreeView:selected': {'background_color': 0, 'secondary_color': 0}, 'TreeView.Frame': {'background_color': 'shade:4280492319;light=4283650900', 'secondary_color': 'shade:4286611584;light=4288585374', 'border_radius': 0, 'scrollbar_size': 10}, 'TreeView.Branch.Line': {'color': 'shade:4284177243', 'background_color': 'shade:4284177243', 'margin': 0}, 'TreeView.Item': {'color': 'shade:4289045925', 'border_width': 1.5, 'border_color': 'shade:4289045925'}, 'TreeView.Item:selected': {'color': 'shade:4289045925'}, 'TreeView.Item.Name': {'background_color': 0, 'color': 'shade:4288782753;light=4292927712'}, 'TreeView.Item.Name:selected': {'color': 'shade:4294952756;light=4291137818'}, 'TreeView.Item.Count': {'background_color': 0, 'color': 'shade:4283453264;light=4288782753'}, 'TreeView.Item.Count:selected': {'color': 'shade:4288782753;light=4292927712'}, 'TreeView.Mark': {'color': 0, 'border_width': 4}, 'TreeView.Mark:selected': {'color': 'shade:4294952756;light=4291137818', 'border_width': 4}, 'GridView.Frame': {'background_color': 'shade:4280492319;light=4283650900', 'secondary_color': 'shade:4286611584;light=4288585374', 'border_radius': 0, 'scrollbar_size': 10}, 'GridView.Grid': {'background_color': 0}, 'GridView.Item': {'background_color': 0, 'color': 'shade:4288782753;light=4292927712'}, 'GridView.Image': {'border_width': 0}, 'GridView.Image:selected': {'border_width': 2, 'border_color': 'shade:4294952756;light=4291137818', 'border_radius': 3.0}, 'GridView.Image.Placeholder': {'image_url': '/home/<USER>/isaacsim/extscache/omni.kit.browser.core-2.3.11/icons/cloud_download.svg', 'color': 'shade:4289243304'}, 'CollapsableFrame': {'background_color': 0, 'secondary_color': 0, 'padding': 0, 'margin': 0}, 'Overview.Frame': {'background_color': 0}, 'Overview.Header.Arrow': {'background_color': 4287795858}, 'Overview.Header.Label': {'color': 4287795858}, 'Overview.Header.Line': {'color': 4285558896}, 'SearchBar.Button': {'background_color': 0, 'padding': 3, 'margin': 0, 'stack_direction': <Direction.LEFT_TO_RIGHT: 0>}, 'SearchBar.Button:hovered': {'background_color': 4282006074}, 'SearchBar.Button:pressed': {'background_color': 'shade:4280492319;light=4283650900'}, 'SearchBar.Button:selected': {'background_color': 'shade:4280492319;light=4283650900'}, 'SearchBar.Button.Image::navigation': {'image_url': '/home/<USER>/isaacsim/extscache/omni.kit.browser.core-2.3.11/icons/navtree.svg', 'color': 'shade:4289243304'}, 'SearchBar.Button.Image::options': {'image_url': '/home/<USER>/isaacsim/extscache/omni.kit.browser.core-2.3.11/icons/options.svg', 'color': 'shade:4289243304'}, 'SearchBar.Button.Label': {'color': 4289506476}, 'Splitter': {'background_color': 0, 'margin_width': 0}, 'Splitter:hovered': {'background_color': 4289753147}, 'Splitter:pressed': {'background_color': 4289753147}}
cl: omni.ui.color_utils.ColorShade  # value = <omni.ui.color_utils.ColorShade object>
