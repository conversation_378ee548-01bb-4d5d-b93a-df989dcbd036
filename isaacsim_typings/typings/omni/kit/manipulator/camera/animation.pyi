from __future__ import annotations
import carb as carb
import omni as omni
import traceback as traceback
__all__: list = ['AnimationEventStream']
class AnimationEventStream:
    _AnimationEventStream__g_instance = None
    @staticmethod
    def get_instance():
        ...
    def _AnimationEventStream__init(self):
        ...
    def _AnimationEventStream__on_event(self, e: carb.events._events.IEvent):
        ...
    def __del__(self):
        ...
    def __init__(self):
        ...
    def add_animation(self, animation_fn: typing.Callable, key: typing.Any, remove_others: bool = True):
        ...
    def destroy(self):
        ...
    def remove_animation(self, key: typing.Any, animation_fn: typing.Callable = None):
        ...
