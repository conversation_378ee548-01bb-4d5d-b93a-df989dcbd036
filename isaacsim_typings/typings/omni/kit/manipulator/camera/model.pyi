from __future__ import annotations
import carb as carb
from omni.kit.manipulator.camera.animation import AnimationEventStream
from omni.kit.manipulator.camera.math import TransformAccumulator
from omni.ui import scene as sc
import omni.ui_scene._scene
import pxr.Gf
from pxr import Gf
import time as time
__all__: list = ['CameraManipulatorModel']
class CameraManipulatorModel(omni.ui_scene._scene.AbstractManipulatorModel):
    @staticmethod
    def _apply_state(*args, **kwds):
        ...
    @staticmethod
    def _item_changed(*args, **kwds):
        ...
    def _CameraManipulatorModel__destroy_animation(self):
        ...
    def _CameraManipulatorModel__fly_mode_lock_view_changed(self, changed_item: carb.dictionary._dictionary.Item, event_type: carb.settings._settings.ChangeEventType):
        ...
    def _CameraManipulatorModel__mark_animating(self, interaction_animating: int):
        ...
    def _CameraManipulatorModel__speed_setting_changed(self, tree_item: carb.dictionary._dictionary.Item, changed_item: carb.dictionary._dictionary.Item, event_type: carb.settings._settings.ChangeEventType, mode: str, setting_scale: float = 1):
        ...
    def _CameraManipulatorModel__validate_arguments(self, name: typing.Union[str, omni.ui_scene._scene.AbstractManipulatorItem], values: typing.Sequence[typing.Union[int, float]] = None) -> omni.ui_scene._scene.AbstractManipulatorItem:
        ...
    def __del__(self):
        ...
    def __init__(self, *args, **kwargs):
        ...
    def _apply_state_tick(self, dt: float = None):
        ...
    def _apply_state_time(self, dt: float, apply_fn: typing.Callable):
        ...
    def _broadcast_mode(self, mode: str):
        ...
    def _calculate_pixel_to_world(self, pos, projection, inv_projection):
        ...
    def _kill_external_animation(self, kill_stream: bool = True, initial_transform = None):
        ...
    def _set_animation_key(self, key: str):
        ...
    def _start_external_events(self, flight_mode: bool = False):
        ...
    def _stop_external_events(self, flight_mode: bool = False):
        ...
    def calculate_pixel_to_world(self, pos):
        ...
    def destroy(self):
        ...
    def get_as_floats(self, item: typing.Union[str, omni.ui_scene._scene.AbstractManipulatorItem]) -> typing.List[float]:
        ...
    def get_as_ints(self, item: typing.Union[str, omni.ui_scene._scene.AbstractManipulatorItem]) -> typing.List[int]:
        ...
    def get_item(self, name: str) -> <omni.ui_scene._scene.AbstractManipulatorItem object>:
        ...
    def set_floats(self, item: typing.Union[str, omni.ui_scene._scene.AbstractManipulatorItem], values: typing.Sequence[int]):
        ...
    def set_ints(self, item: typing.Union[str, omni.ui_scene._scene.AbstractManipulatorItem], values: typing.Sequence[int]):
        ...
class Decay:
    def __init__(self):
        ...
    def apply(self, value: pxr.Gf.Vec3d, dt: float, alpha: float = 1):
        ...
class ModelState:
    def _ModelState__expand_value(self, vec: pxr.Gf.Vec3d, alpha: float):
        ...
    def _ModelState__reduce_value(self, vec: pxr.Gf.Vec3d):
        ...
    def __init__(self, tumble: pxr.Gf.Vec3d = None, look: pxr.Gf.Vec3d = None, move: pxr.Gf.Vec3d = None, fly: pxr.Gf.Vec3d = None):
        ...
    def any_values(self):
        ...
    def apply_alpha(self, alpha: float):
        ...
    @property
    def fly(self):
        ...
    @property
    def look(self):
        ...
    @property
    def move(self):
        ...
    @property
    def tumble(self):
        ...
class Velocity:
    @staticmethod
    def create(model: omni.ui_scene._scene.AbstractManipulatorModel, mode: str, clamp_dt: float = 0.15):
        ...
    def __init__(self, acceleration: typing.Sequence[float], dampening: typing.Sequence[float] = (10), clamp_dt: float = 0.15):
        ...
    def apply(self, value: pxr.Gf.Vec3d, dt: float, alpha: float = 1):
        ...
def _accumulate_values(model: omni.ui_scene._scene.AbstractManipulatorModel, name: str, x: float, y: float, z: float):
    ...
def _flatten_matrix(matrix: pxr.Gf.Matrix4d):
    ...
def _optional_bool(model: omni.ui_scene._scene.AbstractManipulatorModel, item: str, default_value: bool = False):
    ...
def _optional_float(model: omni.ui_scene._scene.AbstractManipulatorModel, item: str, default_value: float = 0):
    ...
def _optional_floats(model: omni.ui_scene._scene.AbstractManipulatorModel, item: str, default_value: typing.Sequence[float] = None):
    ...
def _optional_int(model: omni.ui_scene._scene.AbstractManipulatorModel, item: str, default_value: int = 0):
    ...
def _scalar_or_vector(value: typing.Sequence[float]):
    ...
ALMOST_ZERO: float = 0.0001
