from __future__ import annotations
import carb as carb
import omni.kit.manipulator.camera.gesturebase
from omni.kit.manipulator.camera.gesturebase import CameraGestureBase
from omni.ui import scene as sc
import omni.ui_scene._scene
from pxr import Gf
__all__: list = ['build_gestures', 'PanGesture', 'TumbleGesture', 'LookGesture', 'ZoomGesture']
class LookGesture(omni.kit.manipulator.camera.gesturebase.CameraGestureBase):
    def on_mouse_move(self, mouse_moved):
        ...
class OrthoZoomAperture:
    def __init__(self, model: omni.ui_scene._scene.AbstractManipulatorModel, apertures):
        ...
    def apply(self, model: omni.ui_scene._scene.AbstractManipulatorModel, distance: float):
        ...
    def dirty_items(self, model: omni.ui_scene._scene.AbstractManipulatorModel):
        ...
class OrthoZoomProjection:
    def __init__(self, model: omni.ui_scene._scene.AbstractManipulatorModel, projection):
        ...
    def apply(self, model: omni.ui_scene._scene.AbstractManipulatorModel, distance: float):
        ...
    def dirty_items(self, model: omni.ui_scene._scene.AbstractManipulatorModel):
        ...
class PanGesture(omni.kit.manipulator.camera.gesturebase.CameraGestureBase):
    def on_mouse_move(self, mouse_moved):
        ...
class TumbleGesture(omni.kit.manipulator.camera.gesturebase.CameraGestureBase):
    def on_mouse_move(self, mouse_moved):
        ...
class ZoomGesture(omni.kit.manipulator.camera.gesturebase.CameraGestureBase):
    def _ZoomGesture__setup_ortho_zoom(self):
        ...
    def dirty_items(self, model: omni.ui_scene._scene.AbstractManipulatorModel):
        ...
    def on_began(self, *args, **kwargs):
        ...
    def on_mouse_move(self, mouse_moved):
        ...
def build_gestures(model: omni.ui_scene._scene.AbstractManipulatorModel, bindings: dict = None, manager: omni.ui_scene._scene.GestureManager = None, configure_model: typing.Callable = None):
    ...
kDefaultKeyBindings: dict = {'PanGesture': 'Any MiddleButton', 'TumbleGesture': 'Alt LeftButton', 'ZoomGesture': 'Alt RightButton', 'LookGesture': 'RightButton'}
