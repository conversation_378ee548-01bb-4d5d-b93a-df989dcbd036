from __future__ import annotations
import carb as carb
from omni.kit.manipulator.camera.flight_mode import get_keyboard_input
from omni.kit.manipulator.camera.model import _accumulate_values
from omni.kit.manipulator.camera.model import _flatten_matrix
from omni.kit.manipulator.camera.model import _optional_bool
from omni.kit.manipulator.camera.model import _optional_floats
from omni.ui import scene as sc
import omni.ui_scene._scene
from pxr import Gf
__all__: list = ['CameraGestureBase']
class CameraGestureBase(omni.ui_scene._scene.DragGesture):
    @staticmethod
    def _CameraGestureBase__conform_speed(values):
        ...
    def _CameraGestureBase__apply_as_undoable(self):
        ...
    def __init__(self, model: omni.ui_scene._scene.AbstractManipulatorModel, configure_model: typing.Callable = None, name: str = None, *args, **kwargs):
        ...
    def _accumulate_values(self, key: str, x: float, y: float, z: float):
        ...
    def _disable_flight(self):
        ...
    def _setup_keyboard(self, model, exit_mode: bool) -> bool:
        """
        Setup keyboard and return whether the manipualtor mode (fly) was broadcast to consumers
        """
    def destroy(self):
        ...
    def dirty_items(self, model: omni.ui_scene._scene.AbstractManipulatorModel):
        ...
    def get_rotation_speed(self, secondary):
        ...
    def on_began(self, mouse: typing.Sequence[float] = None):
        ...
    def on_changed(self, mouse: typing.Sequence[float] = None):
        ...
    def on_ended(self):
        ...
    @property
    def center_of_interest(self):
        ...
    @property
    def disable_look(self):
        ...
    @property
    def disable_pan(self):
        ...
    @property
    def disable_tumble(self):
        ...
    @property
    def disable_zoom(self):
        ...
    @property
    def initial_transform(self):
        ...
    @property
    def intertia(self):
        ...
    @property
    def last_transform(self):
        ...
    @property
    def look_speed(self):
        ...
    @property
    def move_speed(self):
        ...
    @property
    def orthographic(self):
        ...
    @property
    def projection(self):
        ...
    @property
    def tumble_speed(self):
        ...
    @property
    def up_axis(self):
        ...
    @property
    def world_speed(self):
        ...
