from __future__ import annotations
import pxr.Gf
from pxr import Gf
__all__: list = ['TransformAccumulator']
class TransformAccumulator:
    def __init__(self, initial_xform: pxr.Gf.Matrix4d):
        ...
    def get_look(self, degrees: pxr.Gf.Vec3d, up_axis: pxr.Gf.Vec3d):
        ...
    def get_rotation_axis(self, up_axis: pxr.Gf.Vec3d):
        ...
    def get_translation(self, amount: pxr.Gf.Vec3d):
        ...
    def get_tumble(self, degrees: pxr.Gf.Vec3d, center_of_interest: pxr.Gf.Vec3d, up_axis: pxr.Gf.Vec3d):
        ...
